{"name": "uni-preset-vue", "type": "module", "version": "0.0.0", "engines": {"node": ">=18.0.0"}, "scripts": {"update-uni-app": "npx @dcloudio/uvm@latest", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-weixin-pro": "uni -p mp-weixin --mode production", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:wx-sit": "uni build -p mp-weixin --mode staging", "build:wx-prod": "uni build -p mp-weixin --mode production", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "commit": "czg", "prepare": "husky", "lint": "eslint --fix"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4050520250307001", "@dcloudio/uni-app-harmony": "3.0.0-4050520250307001", "@dcloudio/uni-app-plus": "3.0.0-4050520250307001", "@dcloudio/uni-components": "3.0.0-4050520250307001", "@dcloudio/uni-h5": "3.0.0-4050520250307001", "@dcloudio/uni-mp-alipay": "3.0.0-4050520250307001", "@dcloudio/uni-mp-baidu": "3.0.0-4050520250307001", "@dcloudio/uni-mp-jd": "3.0.0-4050520250307001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4050520250307001", "@dcloudio/uni-mp-lark": "3.0.0-4050520250307001", "@dcloudio/uni-mp-qq": "3.0.0-4050520250307001", "@dcloudio/uni-mp-toutiao": "3.0.0-4050520250307001", "@dcloudio/uni-mp-weixin": "3.0.0-4050520250307001", "@dcloudio/uni-mp-xhs": "3.0.0-4050520250307001", "@dcloudio/uni-quickapp-webview": "3.0.0-4050520250307001", "@uni-helper/uni-app-types": "1.0.0-alpha.6", "@unocss/core": "0.63.4", "miniprogram-api-typings": "^4.0.4", "pinia": "2.2.4", "pinia-plugin-persistedstate": "3.2.0", "vue": "3.4.21", "vue-i18n": "^9.1.9"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@commitlint/cli": "^17.0.0", "@commitlint/config-conventional": "^17.0.0", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4050520250307001", "@dcloudio/uni-cli-shared": "3.0.0-4050520250307001", "@dcloudio/uni-stacktracey": "3.0.0-4050520250307001", "@dcloudio/vite-plugin-uni": "3.0.0-4050520250307001", "@iconify-json/carbon": "^1.2.5", "@rollup/plugin-commonjs": "^28.0.2", "@types/node": "^22.10.7", "@unocss/eslint-plugin": "^65.4.3", "@unocss/preset-icons": "^65.4.2", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "cz-git": "^1.11.0", "czg": "^1.11.0", "eslint": "^9.18.0", "eslint-plugin-format": "^1.0.1", "husky": "^9.1.7", "lint-staged": "^15.2.11", "sass": "^1.83.4", "typescript": "5.7.3", "unocss": "0.63.4", "unocss-preset-weapp": "^65.4.1", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "5.2.8", "vite-plugin-require-transform": "^1.0.21", "vue-tsc": "^1.0.24"}, "lint-staged": {"*.{vue,ts,tsx}": ["eslint --fix"]}, "config": {"commitizen": {"path": "node_modules/cz-git", "config": "./cz.config.js"}}}