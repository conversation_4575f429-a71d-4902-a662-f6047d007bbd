import antfu from '@antfu/eslint-config'

export default antfu({
  unocss: true,
  ignores: ['./dist/*', './node_modules/*', '**/sm2/*', '**/sm4/*'],
  formatters: true,
  rules: {
    'antfu/if-newline': 'off',
    'no-console': 'off',
    'node/prefer-global/process': 'off',
    'vue/max-attributes-per-line': [
      'error',
      {
        singleline: 2, // 一行最多 2 个属性
        multiline: { max: 1 },
      },
    ],
    'vue/no-parsing-error': [
      'error',
      {
        'invalid-first-character-of-tag-name': false,
      },
    ],
  },
})
