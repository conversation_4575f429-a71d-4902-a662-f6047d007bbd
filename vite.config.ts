/*
 * @Author: houbaog<PERSON>
 * @Date: 2024-11-29 20:28:52
 * @Description:
 * @LastEditTime: 2025-03-10 15:51:21
 * @LastEditors: houbaoguo
 */
import { fileURLToPath, URL } from 'node:url'
import UniApp from '@dcloudio/vite-plugin-uni'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { defineConfig } from 'vite'

import injectCom from './src/plugins/inject-com'
// https://vitejs.dev/config/
export default defineConfig(() => {
  return {
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@img': fileURLToPath(new URL('./src/static/images', import.meta.url)),
      },
    },
    plugins: [
      // 组件自动引入放在uni插件之前：https://github.com/dcloudio/uni-app/issues/3057
      Components({
        dts: 'types/components.d.ts',
      }),
      // 注入组件，fx-toast，fx-confirm
      injectCom(),
      // @ts-expect-error whatever
      UniApp.default(),
      UnoCSS(),
      AutoImport({
        imports: ['vue', 'uni-app', 'pinia'],
        dts: 'types/auto-imports.d.ts',
        vueTemplate: true,
        dirs: ['src/composables'],
      }),
    ],
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          silenceDeprecations: ['legacy-js-api'],
          additionalData: `@use "@/styles/variable.scss" as *;`,
        },
      },
    },
    esbuild: {
      drop:
        process.env.NODE_ENV === 'production'
          ? ['console' as const, 'debugger' as const]
          : [],
    },
  }
})
