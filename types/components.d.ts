/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    FxAmount: typeof import('./../src/components/fx-amount/index.vue')['default']
    FxButton: typeof import('./../src/components/fx-button/index.vue')['default']
    FxCell: typeof import('./../src/components/fx-cell/index.vue')['default']
    FxCheckbox: typeof import('./../src/components/fx-checkbox/index.vue')['default']
    FxCodeInput: typeof import('./../src/components/fx-code-input/index.vue')['default']
    FxConfirm: typeof import('./../src/components/fx-confirm/index.vue')['default']
    FxDraggable: typeof import('./../src/components/fx-draggable/index.vue')['default']
    FxInput: typeof import('./../src/components/fx-input/index.vue')['default']
    FxInputNumber: typeof import('./../src/components/fx-input-number/index.vue')['default']
    FxNavbar: typeof import('./../src/components/fx-navbar/index.vue')['default']
    FxOverlay: typeof import('./../src/components/fx-overlay/index.vue')['default']
    FxPopup: typeof import('./../src/components/fx-popup/index.vue')['default']
    FxScroll: typeof import('./../src/components/fx-scroll/index.vue')['default']
    FxSwiper: typeof import('./../src/components/fx-swiper/index.vue')['default']
    FxSwiperItem: typeof import('./../src/components/fx-swiper-item/index.vue')['default']
    FxTabItem: typeof import('./../src/components/fx-tab-item/index.vue')['default']
    FxTabs: typeof import('./../src/components/fx-tabs/index.vue')['default']
    FxToast: typeof import('./../src/components/fx-toast/index.vue')['default']
    FxTransition: typeof import('./../src/components/fx-transition/index.vue')['default']
    MpHtml: typeof import('./../src/components/mp-html/mp-html.vue')['default']
    Node: typeof import('./../src/components/mp-html/node/node.vue')['default']
  }
}
