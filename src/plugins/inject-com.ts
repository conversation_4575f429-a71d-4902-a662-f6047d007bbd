/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-02-12 10:43:42
 * @Description:
 * @LastEditTime: 2025-02-14 17:34:44
 * @LastEditors: houbaoguo
 */
import type { Plugin } from 'vite'

export default function injectConfirm(): Plugin {
  return {
    name: 'vite-plugin-inject-confirm',
    enforce: 'pre',
    transform(code, id) {
      if (!id.endsWith('.vue')) return

      // 匹配页面组件（排除App.vue和组件文件）
      if (/<script\s+setup[^>]*>/.test(code)
        && !id.includes('App.vue')
        && !id.includes('/components/')) {
        return code.replace(
          /<template>([\s\S]*?)<\/template>/,
          `<template>
            <fx-confirm ref="confirmRef" v-model:fx-confirm-visible="fxConfirmVisible" />
            <fx-toast ref="toastRef" />
            $1
          </template>`,
        )
        // 注入 provide 逻辑
          .replace(
            /<script\s+setup[^>]*>/,
            `<script setup lang="ts">
const fxConfirmVisible = ref(false);
`,
          )
      }

      return code
    },
  }
}
