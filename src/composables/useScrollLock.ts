/**
 * 滚动锁定组合函数
 * 用于在弹窗显示时禁止背景页面滚动
 * 1. 通过fx-overlay和fx-popup的touchmove事件阻止触摸传递
 * 2. 提供全局滚动锁定状态，供其他组件使用
 */

interface ScrollLockOptions {
  /** 是否自动监听visible变化 */
  autoWatch?: boolean
  /** 锁定时是否禁用页面滚动组件 */
  disablePageScroll?: boolean
}

// 全局滚动锁定状态
const globalScrollLocked = ref(false)
const lockCount = ref(0)

export function useScrollLock(options: ScrollLockOptions = {}) {
  const { autoWatch = true, disablePageScroll = true } = options

  // 当前实例的锁定状态
  const isLocked = ref(false)

  /**
   * 锁定滚动
   */
  function lockScroll() {
    if (isLocked.value) return

    try {
      isLocked.value = true
      lockCount.value++

      // 更新全局锁定状态
      if (lockCount.value > 0) {
        globalScrollLocked.value = true
      }

      // 如果启用页面滚动禁用，可以在这里添加额外逻辑
      if (disablePageScroll) {
        // 可以通过事件通知页面组件禁用滚动
        uni.$emit('scroll-lock-change', true)
      }
    }
    catch (error) {
      console.warn('锁定滚动失败:', error)
    }
  }

  /**
   * 解锁滚动
   */
  function unlockScroll() {
    if (!isLocked.value) return

    try {
      isLocked.value = false
      lockCount.value = Math.max(0, lockCount.value - 1)

      // 更新全局锁定状态
      if (lockCount.value === 0) {
        globalScrollLocked.value = false
      }

      // 如果启用页面滚动禁用，恢复滚动
      if (disablePageScroll && lockCount.value === 0) {
        uni.$emit('scroll-lock-change', false)
      }
    }
    catch (error) {
      console.warn('解锁滚动失败:', error)
    }
  }

  /**
   * 切换滚动锁定状态
   */
  function toggleScrollLock(lock?: boolean) {
    const shouldLock = lock !== undefined ? lock : !isLocked.value

    if (shouldLock) {
      lockScroll()
    }
    else {
      unlockScroll()
    }
  }

  /**
   * 监听visible变化自动控制滚动
   */
  function watchVisible(visible: Ref<boolean>) {
    if (!autoWatch) return

    watch(visible, (newVal) => {
      toggleScrollLock(newVal)
    }, { immediate: false })
  }

  // 组件卸载时自动解锁
  onUnmounted(() => {
    if (isLocked.value) {
      unlockScroll()
    }
  })

  return {
    isLocked: readonly(isLocked),
    globalScrollLocked: readonly(globalScrollLocked),
    lockScroll,
    unlockScroll,
    toggleScrollLock,
    watchVisible,
  }
}

/**
 * 简化版本：直接传入visible响应式变量
 */
export function useScrollLockWithVisible(visible: Ref<boolean>, options?: ScrollLockOptions) {
  const scrollLock = useScrollLock(options)

  // 自动监听visible变化
  scrollLock.watchVisible(visible)

  return scrollLock
}
