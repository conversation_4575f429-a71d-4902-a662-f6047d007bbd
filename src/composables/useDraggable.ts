import type { DraggableAdsorbDirection, DraggableBoundary, DraggableEdge, DraggablePosition } from '@/components/fx-draggable/types'
import type { ComponentInternalInstance, Ref } from 'vue'
import { useRect } from './useRect'

export interface UseDraggableOptions {
  /** 元素ID */
  elementId: string
  /** 组件实例 */
  instance?: ComponentInternalInstance | null
  /** 是否启用拖拽 */
  disabled?: Ref<boolean> | boolean
  /** 拖拽边界 */
  boundary?: Ref<DraggableBoundary> | DraggableBoundary
  /** 是否启用贴边吸附 */
  adsorb?: Ref<boolean> | boolean
  /** 贴边吸附的距离阈值 */
  adsorbDistance?: Ref<number> | number
  /** 吸附方向控制 */
  adsorbDirection?: Ref<DraggableAdsorbDirection> | DraggableAdsorbDirection
  /** 初始位置 */
  initialPosition?: Ref<DraggablePosition> | DraggablePosition
  /** 拖拽开始回调 */
  onDragStart?: (position: DraggablePosition) => void
  /** 拖拽移动回调 */
  onDragMove?: (position: DraggablePosition) => void
  /** 拖拽结束回调 */
  onDragEnd?: (position: DraggablePosition) => void
  /** 贴边吸附回调 */
  onAdsorbed?: (edge: DraggableEdge) => void
}

export function useDraggable(options: UseDraggableOptions) {
  const {
    elementId,
    instance,
    disabled = false,
    boundary = {},
    adsorb = true,
    adsorbDistance = 20,
    adsorbDirection = 'all',
    initialPosition = { x: 0, y: 0 },
    onDragStart,
    onDragMove,
    onDragEnd,
    onAdsorbed,
  } = options

  // 响应式状态
  const isDragging = ref(false)
  const isInitialized = ref(false)
  const position = ref<DraggablePosition>({ x: 0, y: 0 })
  const elementSize = ref({ width: 0, height: 0 })
  const screenSize = ref({ width: 0, height: 0 })

  // 拖拽相关状态
  const startPosition = ref<DraggablePosition>({ x: 0, y: 0 })
  const startTouchPosition = ref<DraggablePosition>({ x: 0, y: 0 })

  // 获取响应式值
  const getReactiveValue = <T>(value: Ref<T> | T): T => {
    return unref(value)
  }

  // 获取允许吸附的边
  const getAllowedAdsorbEdges = (): DraggableEdge[] => {
    const direction = getReactiveValue(adsorbDirection)

    if (Array.isArray(direction)) {
      return direction
    }

    switch (direction) {
      case 'horizontal':
        return ['left', 'right']
      case 'vertical':
        return ['top', 'bottom']
      case 'all':
        return ['left', 'right', 'top', 'bottom']
      default:
        return ['left', 'right', 'top', 'bottom']
    }
  }

  // 获取屏幕尺寸
  const getScreenSize = () => {
    const systemInfo = uni.getSystemInfoSync()
    screenSize.value = {
      width: systemInfo.windowWidth,
      height: systemInfo.windowHeight,
    }
  }

  // 获取元素尺寸
  const getElementSize = async (): Promise<void> => {
    try {
      const rect = await useRect(elementId, instance || undefined)
      if (rect && !Array.isArray(rect)) {
        elementSize.value = {
          width: rect.width as number,
          height: rect.height as number,
        }
      }
    }
    catch (error) {
      console.warn('getElementSize error:', error)
    }
  }

  // 计算边界限制
  const getBoundary = () => {
    const defaultBoundary = {
      top: 0,
      left: 0,
      right: screenSize.value.width,
      bottom: screenSize.value.height,
    }

    const currentBoundary = getReactiveValue(boundary)
    return {
      ...defaultBoundary,
      ...currentBoundary,
    }
  }

  // 限制位置在边界内
  const constrainPosition = (x: number, y: number): DraggablePosition => {
    const currentBoundary = getBoundary()
    const { width, height } = elementSize.value

    const minX = currentBoundary.left
    const maxX = currentBoundary.right - width
    const minY = currentBoundary.top
    const maxY = currentBoundary.bottom - height

    return {
      x: Math.max(minX, Math.min(maxX, x)),
      y: Math.max(minY, Math.min(maxY, y)),
    }
  }

  // 贴边吸附逻辑
  const adsorbToEdge = (x: number, y: number): DraggablePosition => {
    const shouldAdsorb = getReactiveValue(adsorb)
    if (!shouldAdsorb) return { x, y }

    const currentBoundary = getBoundary()
    const { width, height } = elementSize.value
    const currentAdsorbDistance = getReactiveValue(adsorbDistance)

    // 如果元素尺寸还没有获取到，先不进行吸附
    if (width === 0 || height === 0) {
      return { x, y }
    }

    let newX = x
    let newY = y
    let adsorbedEdge: DraggableEdge | null = null

    // 计算到各边的距离
    const distanceToLeft = x - currentBoundary.left
    const distanceToRight = currentBoundary.right - (x + width)
    const distanceToTop = y - currentBoundary.top
    const distanceToBottom = currentBoundary.bottom - (y + height)

    // 获取允许吸附的边
    const allowedEdges = getAllowedAdsorbEdges()

    // 找到最近的边进行吸附
    const distances = [
      { edge: 'left' as DraggableEdge, distance: distanceToLeft, newX: currentBoundary.left, newY: y },
      { edge: 'right' as DraggableEdge, distance: distanceToRight, newX: currentBoundary.right - width, newY: y },
      { edge: 'top' as DraggableEdge, distance: distanceToTop, newX: x, newY: currentBoundary.top },
      { edge: 'bottom' as DraggableEdge, distance: distanceToBottom, newX: x, newY: currentBoundary.bottom - height },
    ].filter(item =>
      allowedEdges.includes(item.edge)
      && item.distance >= 0
      && item.distance <= currentAdsorbDistance,
    )

    if (distances.length > 0) {
      // 选择距离最近的边
      const closest = distances.reduce((prev, curr) =>
        curr.distance < prev.distance ? curr : prev,
      )

      newX = closest.newX
      newY = closest.newY
      adsorbedEdge = closest.edge
    }

    if (adsorbedEdge && onAdsorbed) {
      onAdsorbed(adsorbedEdge)
    }

    return { x: newX, y: newY }
  }

  // 触摸开始
  const handleTouchStart = (event: TouchEvent) => {
    const isDisabled = getReactiveValue(disabled)
    if (isDisabled) return

    event.preventDefault()
    event.stopPropagation()

    const touch = event.touches[0]
    startTouchPosition.value = {
      x: touch.pageX || touch.clientX,
      y: touch.pageY || touch.clientY,
    }
    startPosition.value = { ...position.value }
    isDragging.value = true

    onDragStart?.(position.value)
  }

  // 触摸移动
  const handleTouchMove = (event: TouchEvent) => {
    const isDisabled = getReactiveValue(disabled)
    if (isDisabled || !isDragging.value) return

    event.preventDefault()
    event.stopPropagation()

    const touch = event.touches[0]
    const deltaX = (touch.pageX || touch.clientX) - startTouchPosition.value.x
    const deltaY = (touch.pageY || touch.clientY) - startTouchPosition.value.y

    const newX = startPosition.value.x + deltaX
    const newY = startPosition.value.y + deltaY

    const constrainedPosition = constrainPosition(newX, newY)
    position.value = constrainedPosition

    onDragMove?.(constrainedPosition)
  }

  // 触摸结束
  const handleTouchEnd = async (event: TouchEvent) => {
    const isDisabled = getReactiveValue(disabled)
    if (isDisabled || !isDragging.value) return

    event.preventDefault()
    event.stopPropagation()

    isDragging.value = false

    // 确保元素尺寸是最新的
    if (elementSize.value.width === 0 || elementSize.value.height === 0) {
      await getElementSize()
    }

    // 执行贴边吸附
    const adsorbedPosition = adsorbToEdge(position.value.x, position.value.y)
    position.value = adsorbedPosition

    onDragEnd?.(adsorbedPosition)
  }

  // 初始化位置
  const initializePosition = async () => {
    await getElementSize()

    const currentInitialPosition = getReactiveValue(initialPosition)
    if (currentInitialPosition.x !== undefined || currentInitialPosition.y !== undefined) {
      const constrainedPosition = constrainPosition(
        currentInitialPosition.x ?? 0,
        currentInitialPosition.y ?? 0,
      )
      position.value = constrainedPosition
    }
    else {
      // 默认位置：右下角
      const currentBoundary = getBoundary()
      const defaultX = currentBoundary.right - elementSize.value.width - 20
      const defaultY = currentBoundary.bottom - elementSize.value.height - 100
      position.value = constrainPosition(defaultX, defaultY)
    }

    // 标记为已初始化
    nextTick(() => {
      isInitialized.value = true
    })
  }

  // 设置位置
  const setPosition = (newPosition: Partial<DraggablePosition>) => {
    const constrainedPosition = constrainPosition(
      newPosition.x ?? position.value.x,
      newPosition.y ?? position.value.y,
    )
    position.value = constrainedPosition
  }

  // 重置位置
  const resetPosition = () => {
    const currentInitialPosition = getReactiveValue(initialPosition)
    setPosition(currentInitialPosition)
  }

  return {
    // 状态
    isDragging: readonly(isDragging),
    isInitialized: readonly(isInitialized),
    position: readonly(position),
    elementSize: readonly(elementSize),
    screenSize: readonly(screenSize),

    // 方法
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    initializePosition,
    setPosition,
    resetPosition,
    getScreenSize,
    getElementSize,
    constrainPosition,
    adsorbToEdge,
  }
}
