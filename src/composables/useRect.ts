/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-06-17 17:54:46
 * @Description:
 * @LastEditTime: 2025-06-17 17:56:50
 * @LastEditors: houbaoguo
 */
import type { ComponentInternalInstance } from 'vue'
import { useSelectorQuery } from './useSelectorQuery'

export function useRect(id: string, instance?: ComponentInternalInstance): Promise<UniApp.NodeInfo> {
  const { getSelectorNodeInfo } = useSelectorQuery(instance)
  return getSelectorNodeInfo(`#${id}`)
}
