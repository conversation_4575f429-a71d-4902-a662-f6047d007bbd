/*
 * @Author: houbaoguo
 * @Date: 2025-02-12 11:19:03
 * @Description:
 * @LastEditTime: 2025-03-05 11:34:47
 * @LastEditors: houbaoguo
 */
import type Confirm from '@/components/fx-confirm/index.vue'
import type { ConfirmOptions } from '@/components/fx-confirm/types'
import type Toast from '@/components/fx-toast/index.vue'
import type { ToastOptions } from '@/components/fx-toast/types'
import { useInstanceStore } from '@/store'
/* interface ConfirmService {
  show: (options: ConfirmOptions & {
    onConfirm: () => void
    onCancel: () => void
  }) => void
} */

// 递归向上找页面的实例
function findPageInstance(instanceProxy: any) {
  if (!instanceProxy) return null
  if (instanceProxy.$mpType === 'page') {
    return instanceProxy
  }
  return findPageInstance(instanceProxy?.$parent)
}

export function useConfirm() {
  const instanceStore = useInstanceStore()
  const instance = getCurrentInstance() || instanceStore.currentInstance
  const pageInstanceProxy = findPageInstance(instance?.proxy)
  // console.log('🚀 ~ useConfirm ~ instance:', instance)
  const showConfirm = (options: ConfirmOptions) => {
    const confirmRef = pageInstanceProxy?.$refs?.confirmRef as InstanceType<typeof Confirm>
    return new Promise<boolean>((resolve, reject) => {
      confirmRef.show({
        ...options,
        onConfirm: () => resolve(true),
        onCancel: () => reject(new Error('取消')),
      })
    })
  }

  return { confirm: showConfirm }
}

export function useToast() {
  const instanceStore = useInstanceStore()
  const instance = getCurrentInstance() || instanceStore.currentInstance
  const pageInstanceProxy = findPageInstance(instance?.proxy)
  // console.log('🚀 ~ useToast ~ instance:', instance)
  const showToast = (options: ToastOptions | string) => {
    const toastRef = pageInstanceProxy?.$refs?.toastRef as InstanceType<typeof Toast>
    console.log('instance', instance)
    if (typeof options === 'string') {
      toastRef.show({
        message: options,
        duration: 1500,
      })
    }
    else {
      toastRef.show(options)
    }
  }
  return { toast: showToast }
}
