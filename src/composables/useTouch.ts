/*
 * @Author: houbaoguo
 * @Date: 2025-06-17 18:05:37
 * @Description:
 * @LastEditTime: 2025-06-17 18:05:42
 * @LastEditors: houbaoguo
 */
const MIN_DISTANCE = 10

type Direction = '' | 'vertical' | 'horizontal'

function getDirection(x: number, y: number) {
  if (x > y && x > MIN_DISTANCE)
    return 'horizontal'

  if (y > x && y > MIN_DISTANCE)
    return 'vertical'

  return ''
}

export function useTouch() {
  const startX = ref(0)
  const startY = ref(0)
  const moveX = ref(0)
  const moveY = ref(0)
  const deltaX = ref(0)
  const deltaY = ref(0)
  const offsetX = ref(0)
  const offsetY = ref(0)
  const direction = ref<Direction>('')

  const isVertical = () => direction.value === 'vertical'
  const isHorizontal = () => direction.value === 'horizontal'

  const reset = () => {
    deltaX.value = 0
    deltaY.value = 0
    offsetX.value = 0
    offsetY.value = 0
    direction.value = ''
  }

  const start = ((event: TouchEvent) => {
    reset()
    startX.value = event.touches[0].clientX
    startY.value = event.touches[0].clientY
  }) as EventListener

  const move = ((event: TouchEvent) => {
    const touch = event.touches[0]
    deltaX.value = touch.clientX - startX.value
    deltaY.value = touch.clientY - startY.value
    moveX.value = touch.clientX
    moveY.value = touch.clientY
    offsetX.value = Math.abs(deltaX.value)
    offsetY.value = Math.abs(deltaY.value)

    if (!direction.value)
      direction.value = getDirection(offsetX.value, offsetY.value)
  }) as EventListener

  return {
    move,
    start,
    reset,
    startX,
    startY,
    moveX,
    moveY,
    deltaX,
    deltaY,
    offsetX,
    offsetY,
    direction,
    isVertical,
    isHorizontal,
  }
}
