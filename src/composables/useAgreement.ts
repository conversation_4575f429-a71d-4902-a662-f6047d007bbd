/*
 * @Author: houbaoguo
 * @Date: 2025-04-30 14:19:57
 * @Description:
 * @LastEditTime: 2025-04-30 14:31:20
 * @LastEditors: houbaoguo
 */
import { type AgreementListRes, type AgreementType, AgreementTypes } from '@/api/types'
import { useAgreementStore } from '@/store'

export default function useAgreement(targetType: AgreementType) {
  const agreementStore = useAgreementStore()
  const agreementNameList = computed(() => agreementStore?.agreementNameList)

  const agreementName = computed(() => {
    return agreementNameList.value.find((item: AgreementListRes) => item.agreementType === targetType)?.agreementName
  })

  const tryCount = ref(0)
  const maxTries = 1

  watchEffect(async () => {
    if (!agreementName.value && tryCount.value < maxTries) {
      tryCount.value++
      await agreementStore.getAgreementNameListHandle({
        agreementType: Object.values(AgreementTypes),
      })
    }
  })

  return {
    agreementN<PERSON>,
    agreementNameList,
  }
}
