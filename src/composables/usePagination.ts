/**
 * 分页加载组合函数
 * 用于处理列表数据的分页加载逻辑
 */
import { reactive, ref } from 'vue'

export interface PaginationOptions {
  /** 每页数据量，默认10 */
  pageSize?: number
  /** 是否自动加载第一页，默认true */
  autoLoad?: boolean
}

export interface PaginationParams {
  page: number
  limit: number
}

export interface PaginationResponse<T> {
  list: T[]
  page: number
  limit: number
  totalPage: number
  total: number
}

export function usePagination<T = any, P = Record<string, any>>(
  fetchFunction: (params: PaginationParams & P) => Promise<{ data: PaginationResponse<T> }>,
  options: PaginationOptions = {},
) {
  const {
    pageSize = 10,
    autoLoad = true,
  } = options

  // 响应式状态
  const loading = ref(false)
  const refreshing = ref(false)
  const list = ref<T[]>([])
  const finished = ref(false)
  const error = ref<string | null>(null)

  // 分页参数
  const pagination = reactive({
    page: 1,
    limit: pageSize,
    total: 0,
    totalPage: 0,
  })

  /**
   * 重置状态
   */
  const reset = () => {
    list.value = []
    pagination.page = 1
    pagination.total = 0
    pagination.totalPage = 0
    finished.value = false
    error.value = null
  }

  /**
   * 加载数据
   */
  const loadData = async (isRefresh = false) => {
    if (loading.value || (finished.value && !isRefresh)) {
      return
    }

    try {
      if (isRefresh) {
        refreshing.value = true
        reset()
      }
      else {
        loading.value = true
      }

      const params: PaginationParams = {
        page: pagination.page,
        limit: pagination.limit,
      }

      const response = await fetchFunction(params)
      const data = response.data

      if (data && data.list) {
        if (isRefresh) {
          list.value = (data.list as any)
        }
        else {
          list.value.push(...(data.list as any))
        }

        pagination.total = data.total
        pagination.totalPage = data.totalPage

        // 判断是否还有更多数据
        finished.value = pagination.page >= pagination.totalPage

        // 如果还有更多数据，页码+1
        if (!finished.value) {
          pagination.page++
        }
      }
      else {
        finished.value = true
      }

      error.value = null
    }
    catch (err) {
      console.error('加载数据失败:', err)
      error.value = err instanceof Error ? err.message : '加载失败'

      // 如果是第一页加载失败，显示错误状态
      if (pagination.page === 1) {
        list.value = []
      }
    }
    finally {
      loading.value = false
      refreshing.value = false
    }
  }

  /**
   * 刷新数据（重新加载第一页）
   */
  const refresh = () => {
    loadData(true)
  }

  /**
   * 加载更多数据
   */
  const loadMore = () => {
    if (!finished.value && !loading.value) {
      return loadData(false)
    }
    return Promise.resolve()
  }

  /**
   * 初始化 - 总是加载第一页数据
   */
  const init = () => {
    loadData(true)
  }

  // 如果设置了自动加载，则立即执行初始化
  if (autoLoad) {
    init()
  }

  return {
    // 状态
    loading,
    refreshing,
    list,
    finished,
    error,
    pagination,

    // 方法
    loadData,
    refresh,
    loadMore,
    reset,
    init,
  }
}
