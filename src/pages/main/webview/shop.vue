<!--
 * @Author: houbaoguo
 * @Date: 2025-02-25 17:11:23
 * @Description:
 * @LastEditTime: 2025-07-11 16:10:26
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
const url = ref('')

onLoad((options) => {
  url.value = decodeURIComponent(options?.url || '')
  uni.setNavigationBarTitle({
    title: options?.title || '',
  })
})
</script>

<template>
  <web-view :src="url" />
</template>

<style scoped>

</style>
