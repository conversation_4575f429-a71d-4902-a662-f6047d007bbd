<script setup lang="ts">

</script>

<template>
  <view class="empty">
    <image class="empty__img" src="@img/common/list-empty.png" />
    <view class="empty__text">
      商品不存在或已下架
    </view>
  </view>
</template>

<style scoped lang="scss">
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  &__img {
    width: 264rpx;
    height: 264rpx;
  }
  &__text {
    font-size: 28rpx;
    color: #787c89;
  }
}
</style>
