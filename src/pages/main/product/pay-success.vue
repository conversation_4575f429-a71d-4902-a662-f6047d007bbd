<!--
 * @Author: houbaoguo
 * @Date: 2025-06-20 09:27:57
 * @Description:
 * @LastEditTime: 2025-07-31 17:13:56
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { GoodsListResItem } from '@/api/types/goods'
import { getHotGoodsList, getOrderStatus } from '@/api'
import { usePagination } from '@/composables/usePagination'
import { useRouter } from '@/composables/useRouter'
import { useUserStore } from '@/store'
import GoodsListItem from './components/goods-list-item.vue'
import shareGuidePopup from './components/share-guide-popup.vue'

const { toHref } = useRouter()
const { toast } = useToast()
const userStore = useUserStore()
const { mescrollInit } = useScroll(onPageScroll, onReachBottom)
const { globalScrollLocked } = useScrollLock()
const showShareGuidePopup = ref(false)
const orderNo = ref('')

// 根据滚动锁定状态动态配置fx-scroll
const scrollConfig = computed(() => ({
  auto: false,
  use: !globalScrollLocked.value, // 当有popup时禁用上拉加载
  offset: 50,
  textLoading: '加载中...',
  textNoMore: '没有更多了',
}))

// 使用分页组合函数
const {
  list: goodsList,
  finished,
  loadMore,
} = usePagination<GoodsListResItem>(getHotGoodsList, {
  pageSize: 4,
})

// 处理商品点击
function handleGoodsClick(item: GoodsListResItem) {
  toHref(`/pages/main/product/details?id=${item.id}&isLogin=true`)
}

// 处理上拉加载
async function handleScrollToLower(mescroll: any) {
  try {
    await loadMore()
    // 加载成功，告诉mescroll组件当前数据状态
    mescroll.endSuccess(goodsList.value.length, !finished.value)
  }
  catch {
    // 加载失败
    mescroll.endErr()
  }
}

// 查看订单
async function handleViewOrder() {
  const status = await handleQueryOrderStatus()
  if (!status) {
    return
  }
  const url = `${import.meta.env.VITE_APP_H5_URL}/dm-shop-uniapp/pages/order/detail?order_id=${orderNo.value}&token=${userStore.access_token}`
  toHref(`/pages/main/webview/index?url=${encodeURIComponent(url)}&title=订单详情`)
}

// 查询订单状态
async function handleQueryOrderStatus() {
  try {
    const res = await getOrderStatus({ orderNo: orderNo.value })
    console.log(res)
    if (res.data.payStatus === 2) {
      toast('订单处理中，请稍后查看')
      return false
    }
    return true
  }
  catch (error) {
    console.log(error)
    return false
  }
}

onLoad((options) => {
  orderNo.value = options?.orderNo || ''
})
</script>

<template>
  <view class="pay-success">
    <image src="@img/product/page-bg.png" class="product-details__bg" />
    <fx-scroll
      class="pay-success__scroll"
      :up="scrollConfig"
      :down="{
        use: false,
      }"
      @init="mescrollInit"
      @up="handleScrollToLower"
    >
      <fx-navbar background="transparent" left-color="#787C89" />

      <view class="pay-success__content">
        <view class="title">
          下单成功
        </view>
        <fx-button
          ml-auto
          mr-auto
          mt-24rpx
          block
          h-64rpx
          w-192rpx
          radius="50px"
          :custom-style="{ background: '#fff', color: '#454A5B', fontSize: '28rpx', lineHeight: '40rpx' }"
          @tap="handleViewOrder"
        >
          查看订单
        </fx-button>
        <view class="guide-tip-wrap">
          <view class="desc">
            <view class="desc-title">
              🎉限时推广活动进行中
            </view>
            <view class="desc-sub-title">
              可在 App 推广商品赚奖励哦～
            </view>
          </view>
          <fx-button
            radius="100"
            :custom-style="{ 'with': '160rpx', 'height': '64rpx', 'fontSize': '28rpx', 'lineHeight': '40rpx', 'color': '#FF531A', '--fx-primary-color': '#FFFFFF' }"
            @tap="showShareGuidePopup = true"
          >
            参与活动
          </fx-button>
        </view>
        <view class="recommend">
          <view class="recommend__title">
            - 特惠商城推荐 -
          </view>
          <view class="recommend__list">
            <view
              v-for="item in goodsList"
              :key="item.id"
              class="recommend__list-item"
            >
              <GoodsListItem
                :item="item"
                @item-tap="handleGoodsClick(item)"
              />
            </view>
          </view>
        </view>
      </view>
    </fx-scroll>
    <share-guide-popup v-model:visible="showShareGuidePopup" />
  </view>
</template>

<style scoped lang="scss">
.pay-success {
  position: relative;
  min-height: 100vh;
  background: #f5f5f5;
  .product-details__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 540rpx;
    z-index: 0;
  }
  .pay-success__scroll {
    height: 100vh;
  }
  .pay-success__content {
    position: relative;
    z-index: 1;
    // padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
    .title {
      font-size: 40rpx;
      font-weight: 600;
      color: #151d32;
      text-align: center;
      line-height: 56rpx;
    }
    .guide-tip-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 48rpx 24rpx 0;
      padding: 32rpx;
      border-radius: 32rpx;
      background: linear-gradient(-90deg, #ffa01a 0%, #ff531a 100%);
      .desc {
        flex: 1;
        .desc-title {
          font-size: 36rpx;
          font-weight: 500;
          color: #ffffff;
          line-height: 50rpx;
        }
        .desc-sub-title {
          font-size: 30rpx;
          font-weight: 400;
          color: #ffffff;
          line-height: 50rpx;
        }
      }
    }
    .recommend {
      padding: 0 22rpx;
      .recommend__title {
        font-size: 30rpx;
        color: #787c89;
        line-height: 42rpx;
        text-align: center;
        margin-top: 32rpx;
      }
      .recommend__list {
        margin-top: 24rpx;
        &-item {
          &:not(:last-child) {
            margin-bottom: 24rpx;
          }
        }
      }
    }
  }
}
</style>
