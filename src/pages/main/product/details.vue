<!--
 * @Author: houbaoguo
 * @Date: 2025-06-16 17:44:04
 * @Description:
 * @LastEditTime: 2025-07-31 14:36:43
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { ComponentInternalInstance } from 'vue'
import { type CreateOrderParams, OrderType } from '@/api/types/goods'
// eslint-disable-next-line ts/ban-ts-comment
// @ts-expect-error
import mpHtml from '@/components/mp-html/mp-html'
import { useUserStore } from '@/store'
import { getCurrentPageList } from '@/utils'
import descSection from './components/desc-section.vue'
import goodsCardItem from './components/goods-card-item.vue'
import loginConfirm from './components/login-confirm.vue'
import orderConfirmPopup from './components/order-confirm-popup.vue'
import productImagesSwiper from './components/product-images-swiper.vue'
import shareGuidePopup from './components/share-guide-popup.vue'
import productSkeleton from './components/skeleton.vue'
import { useOrder } from './hooks/useOrder'
import { useProductDetail } from './hooks/useProductDetail'
import { type TabItem, useScrollNavigation } from './hooks/useScrollNavigation'

const userStore = useUserStore()
const { toHref } = useRouter()
const { toast } = useToast()

// 标签页配置
const tabsList: TabItem[] = [
  { id: 'goods-info', name: '商品' },
  { id: 'goods-desc', name: '详情' },
  { id: 'goods-recommend', name: '推荐' },
]

// 使用滚动导航组合函数
const {
  // scrollTop,
  opacityNum,
  curTabIndex,
  navBackground,
  handlePageScroll,
  scrollToSection,
  initScrollNavigation,
  recalculate,
} = useScrollNavigation(tabsList)

// 使用商品详情组合函数
const {
  productInfo,
  attrInfo,
  swiperList,
  similarDiscountList,
  pageLoading,
  loadPageData,
  updateProductByChangeAttr,
} = useProductDetail(callbackChangeToggleDesc)

// 使用订单相关组合函数
const {
  loading: completeOrderLoading,
  preOrderLoading,
  handlePreOrder,
  handleCompleteOrder,
} = useOrder()

// 其他页面状态
const instance = getCurrentInstance() as ComponentInternalInstance
const productId = ref('')
const promoterId = ref('')
const inviteNo = ref('')
const showLoginConfirm = ref(false)
const toggleDesc = ref(false)
const showShareGuidePopup = ref(false)
const showOrderConfirmPopup = ref(false)
const productNum = ref(1)
const totalPayFee = ref('0')
const freightFee = ref('0')
const orderConfirmPopupRef = ref()
const showImagePreview = ref(false)
const imagePreviewIndex = ref(0)
const addressId = ref(-1)

// 是否登录
const isLogin = computed(() => {
  return !!userStore.access_token
})

// 是否显示导航栏返回按钮
const isShowBack = computed(() => {
  // 如果当前路由栈长度大于1，则显示返回按钮
  return getCurrentPageList().length > 1
})

// 计算margin-bottom
const computedMarginBottom = computed(() => {
  const isAndroid = uni.getSystemInfoSync().platform === 'android'
  return isAndroid ? { marginBottom: '-6rpx' } : {}
})

// 展开收起
function toggleDescChange() {
  toggleDesc.value = !toggleDesc.value
  // 内容变化后重新计算位置
  recalculate(instance, 300)
}

// callbackChangeToggleDesc
function callbackChangeToggleDesc() {
  toggleDesc.value = true
  recalculate(instance, 300)
}

// 引导登录或静默登录
async function guideLoginOrSilentLogin(isGuide = true) {
  // uni.showLoading({
  //   mask: true,
  // })
  try {
    // 如果未获取微信授权信息，获取微信授权信息
    if (!userStore.wechatOpenId) {
      await userStore.getWechatAuthInfo()
    }
    // 如果未绑定微信，引导用户绑定微信
    if (!userStore.checkIsBindWechat) {
      // 引导用户绑定微信
      if (isGuide) {
        showLoginConfirm.value = true
      }
      return false
    }
    // 静默登录
    await userStore.loginHandle({
      marketingGroupNo: inviteNo.value,
    })
    return true
  }
  catch (error) {
    console.log('error', error)
    return false
  }
  finally {
    // uni.hideLoading()
  }
}

// 同步收货地址id
function handleChangeAddressId(id: number) {
  console.log('🚀 ~ handleChangeAddressId ~ addressId:', id)
  addressId.value = id
}

// 预下单
async function handlePreOrderFn() {
  try {
    const params = {
      source: 'weixin' as const,
      addressId: addressId.value,
      preOrderType: OrderType.BUY_NOW,
      orderDetails: [
        {
          productId: productId.value,
          attrValueId: attrInfo.value.productSelect.unique,
          productNum: productNum.value,
        },
      ],
    }

    const { preOrderData, loadPreOrderData } = await handlePreOrder(params)
    console.log('🚀 ~ handleBuy ~ loadPreOrderData:', loadPreOrderData)
    console.log('🚀 ~ handleBuy ~ preOrderData:', preOrderData)
    totalPayFee.value = loadPreOrderData.orderInfoVo?.payFee || '--'
    freightFee.value = loadPreOrderData.orderInfoVo?.freightFee || '--'
  }
  catch (error) {
    console.error('预下单失败:', error)
  }
}

// 立即购买
async function handleBuy() {
  // 如果未登录，引导登录或静默登录
  try {
    uni.showLoading({
      mask: true,
    })
    if (!isLogin.value) {
      const status = await guideLoginOrSilentLogin()
      if (!status) {
        uni.hideLoading()
        return
      }
    }
    // 预下单
    await handlePreOrderFn()
    showOrderConfirmPopup.value = true
  }
  catch (error) {
    console.error('handleBuy:', error)
  }
  finally {
    uni.hideLoading()
  }
}

// 确认订单
async function handleConfirmOrder(createOrderParams: Omit<CreateOrderParams, 'preOrderNo'>) {
  try {
    const preOrderParams = {
      source: 'weixin' as const,
      addressId: createOrderParams.addressId,
      preOrderType: OrderType.BUY_NOW,
      orderDetails: [
        {
          productId: productId.value,
          attrValueId: attrInfo.value.productSelect.unique,
          productNum: productNum.value,
        },
      ],
    }

    const { payResult, createOrderResult } = await handleCompleteOrder(preOrderParams, createOrderParams)
    console.log('🚀 ~ handleConfirmOrder ~ payResult:', payResult)
    showOrderConfirmPopup.value = false
    toHref(`/pages/main/product/pay-success?orderNo=${createOrderResult.orderNo}`)
  }
  catch (error: any) {
    showOrderConfirmPopup.value = false
    if (error?.errMsg === 'requestPayment:fail cancel') {
      toast('取消支付')
      return
    }
    const msg = error?.data?.msg || error?.msg || error?.message || '系统异常,请稍后重试'
    toast(msg)
    console.error('handleConfirmOrder:', error)
  }
}

function handleGoToDetail(item: any) {
  console.log(item)
  toHref(`/pages/main/product/details?id=${item.id}&isLogin=${isLogin.value}`)
}

// 查看订单

function handleViewOrder() {
  const url = `${import.meta.env.VITE_APP_H5_URL}/dm-shop-uniapp/pages/order/index?token=${userStore.access_token}`
  toHref(`/pages/main/webview/index?url=${encodeURIComponent(url)}&title=我的订单`)
}

// 图片预览
function handleImagePreview(index: number) {
  console.log('handleImagePreview', index)
  showImagePreview.value = true
  imagePreviewIndex.value = index
  uni.previewImage({
    current: index,
    urls: swiperList.value,
  })
}

// 默认参数判断处理
function handlePageParams(options: any) {
  const source = options?.s || 'share'
  if (source === 'scheme') {
    productId.value = options?.id || '10'
    promoterId.value = 'UN20240202110536135458'
  }
  else {
    productId.value = options?.id || ''
    promoterId.value = options?.promoterId || ''
    inviteNo.value = options?.inviteNo || ''
  }

  if (!productId.value && !promoterId.value && !inviteNo.value) {
    toHref('/pages/main/introduction/index', 'redirectTo')
    return false
  }
  return true
}
// attrInfo.value.productSelect.unique，productNum,addressId变化时执行，且当前为登录状态且订单确认弹窗显示时执行
watch([() => attrInfo.value.productSelect, productNum, addressId], () => {
  isLogin.value && showOrderConfirmPopup.value && handlePreOrderFn()
}, { deep: true })

watch(isLogin, (newVal) => {
  if (newVal) {
    // 获取收货地址，未登录状态到登录状态时触发
    orderConfirmPopupRef.value?.getAddressListHandler()
  }
}, { immediate: true })

// 监听页面加载状态，在数据加载完成后初始化滚动导航
watch(pageLoading, async (loading) => {
  if (!loading) {
    // 等待DOM更新完成
    await nextTick()
    // 初始化滚动导航
    await initScrollNavigation(instance)
    // 再次确保初始化成功
    nextTick(async () => {
      await initScrollNavigation(instance)
    })
  }
})

// 页面生命周期
onPageScroll(handlePageScroll)

onReady(async () => {
  // 如果页面还在加载中，等待加载完成后再初始化滚动导航
  if (pageLoading.value) {
    // 通过watch监听pageLoading变化来初始化
    return
  }
  await initScrollNavigation(instance)
})

onLoad(async (options: any) => {
  console.log('🚀 ~ onLoad ~ options:', options)
  try {
    const isNext = handlePageParams(options)
    if (!isNext) {
      return
    }

    if (options?.isLogin !== 'true') {
      // 静默登录
      await guideLoginOrSilentLogin(false)
    }
    // 如果是登录状态，获取收货地址
    if (isLogin.value) {
      nextTick(() => {
        orderConfirmPopupRef.value?.getAddressListHandler()
      })
    }

    // 加载页面数据
    if (productId.value) {
      await loadPageData(productId.value)
    }
    else {
      toHref('/pages/main/product/empty', 'redirectTo')
    }
  }
  catch (error) {
    console.error('页面初始化失败:', error)
  }
})
</script>

<template>
  <view class="product-details" :class="{ 'reset-padding': pageLoading }">
    <image
      v-if="!pageLoading"
      src="@img/product/page-bg.png"
      class="product-details__bg"
    />
    <fx-navbar
      v-if="!pageLoading"
      :left-show="isShowBack"
      left-color="#333"
      :background="navBackground"
    >
      <template #content>
        <view v-if="opacityNum > 0" class="product-details__title-content">
          <view class="nav-tab" :style="{ opacity: opacityNum }">
            <view
              v-for="(item, index) in tabsList"
              :key="item.id"
              class="nav-tab__item"
              :class="{ active: curTabIndex === index }"
              @tap="scrollToSection(item.id)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </template>
    </fx-navbar>
    <!-- 骨架屏 -->
    <product-skeleton v-if="pageLoading" />

    <!-- 主体内容 -->
    <view v-if="!pageLoading" class="product-details__content">
      <!-- 产品详情图片轮播，产品信息 -->
      <view id="goods-info" overflow-hidden>
        <product-images-swiper :swiper-list="swiperList" @image-preview="handleImagePreview" />
        <view class="product-info">
          <view class="product-info__price">
            <text class="sale-price">
              到手价
            </text>
            <fx-amount
              show-symbol
              :style="computedMarginBottom"
              mb--4rpx
              symbol-size="28rpx"
              symbol-font-weight="400"
              :amount="productInfo?.price"
              integer-size="48rpx"
              decimal-size="28rpx"
              color="#FF531A"
              font-weight="500"
            />
            <fx-amount
              :style="computedMarginBottom"
              show-line-through
              show-symbol
              mb--4rpx
              ml-16rpx
              :amount="productInfo?.otPrice"
              size="26rpx"
              color="#787C89"
              font-weight="400"
            />
          </view>
          <view class="product-info__title ellipsis-2-lines">
            {{ productInfo?.storeName }}
          </view>
          <view class="tag-box">
            <view v-if="productInfo.cateIds.includes('PRE_SELL')" class="product-info__tag fill-tag">
              预售
            </view>
            <view v-if="productInfo.cateIds.includes('FREE_SHIPPING')" class="product-info__tag plain-tag">
              包邮
            </view>
            <view v-if="productInfo.cateIds.includes('HOT_PRODUCT')" class="product-info__tag plain-tag">
              热卖产品
            </view>
          </view>
        </view>
      </view>

      <!-- 产品详情 -->
      <view id="goods-desc" overflow-hidden>
        <view class="card-section desc">
          <view class="card-section__title" @tap="toggleDescChange">
            <view class="card-section__title-text">
              产品详情
            </view>
            <image
              v-if="!toggleDesc"
              h-40rpx
              w-40rpx
              src="@img/common/arrow-right.png"
            />
            <image
              v-else
              h-40rpx
              w-40rpx
              rotate-90
              src="@img/common/arrow-right.png"
            />
          </view>
          <view class="card-section__content" :class="{ 'card-section__content__active': toggleDesc }">
            <mp-html
              :content="productInfo?.content"
              :tag-style="{
                img: 'width:100%;display:block;',
                table: 'width:100%',
                video: 'width:100%',
              }"
            />
            <desc-section @toggle-change="recalculate(instance, 300)" />
          </view>
        </view>
      </view>

      <!-- 相似优惠 -->
      <view id="goods-recommend" overflow-hidden>
        <view v-if="similarDiscountList.length" class="card-section similar-discount">
          <view class="card-section__title">
            <view class="card-section__title-text">
              相似优惠
            </view>
          </view>
          <view class="card-section__content">
            <view class="goods-card-list">
              <view
                v-for="item in similarDiscountList"
                :key="item.id"
                class="goods-card-list__item"
              >
                <goods-card-item
                  :item="item"
                  @card-tap="handleGoToDetail(item)"
                />
              </view>
              <view
                v-if="similarDiscountList.length % 3 !== 0"
                class="goods-card-list__item"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view v-if="!pageLoading" class="product-details__footer">
      <view
        v-if="!pageLoading && isLogin"
        class="own-order"
        @tap="handleViewOrder"
      >
        <image class="icon" src="@img/product/own-order.png" />
        <text class="text">
          我的订单
        </text>
      </view>
      <fx-button
        radius="100"
        plain
        mr-8rpx
        w-256rpx
        fw-500
        :custom-style="{ 'height': '88rpx', 'width': '100%', 'fontSize': '32rpx', '--fx-primary-color': '#FF5D44' }"
        @tap="showShareGuidePopup = true"
      >
        <view class="share-btn">
          <text class="share-btn__text">
            分享有奖
          </text>
          <text class="share-btn__sub-text">
            快来试试吧
          </text>
        </view>
      </fx-button>
      <fx-button
        radius="100"
        ml-8rpx
        flex-1
        fw-500
        :custom-style="{ height: '88rpx', width: '100%', fontSize: '32rpx', lineHeight: '48rpx', background: 'linear-gradient(270deg, #FF3433 0%, #FF8133 100%)', border: 'none' }"
        @tap="handleBuy"
      >
        立即购买
      </fx-button>
    </view>
  </view>
  <!-- 登录确认弹窗 -->
  <login-confirm
    v-model:visible="showLoginConfirm"
    :marketing-group-no="inviteNo"
    @success="handleBuy"
  />
  <!-- 分享引导弹窗 -->
  <share-guide-popup v-model:visible="showShareGuidePopup" />
  <!-- 确认订单弹窗 -->
  <order-confirm-popup
    ref="orderConfirmPopupRef"
    v-model:visible="showOrderConfirmPopup"
    v-model:product-num="productNum"
    :attr-info="attrInfo"
    :total-pay-fee="totalPayFee"
    :freight-fee="freightFee"
    :pre-order-loading="preOrderLoading"
    :complete-order-loading="completeOrderLoading"
    :promoter-id="promoterId"
    @change-attr="updateProductByChangeAttr"
    @confirm-order="handleConfirmOrder"
    @change-address-id="handleChangeAddressId"
  />
</template>

<style scoped lang="scss">
.product-details {
  position: relative;
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: calc(180rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
  &.reset-padding {
    padding-bottom: 0;
  }
  .product-details__bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 540rpx;
    z-index: 0;
  }
  .bubble-tip-swiper {
    position: fixed;
    top: 192rpx;
    left: 24rpx;
    z-index: 10;
    max-width: calc(100vw - 48rpx);
    width: 100%;
    // height: 62rpx;
  }
  &__title-content {
    position: relative;
    z-index: 1;
    margin: 0 auto;
    width: 276rpx;
    .nav-tab {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .nav-tab__item {
        font-size: 30rpx;
        color: #151d32;
        font-weight: 400;
        line-height: 42rpx;
        &.active {
          position: relative;
          font-weight: 500;
          &::after {
            content: '';
            position: absolute;
            bottom: 4rpx;
            left: 0;
            width: 100%;
            height: 8rpx;
            border-radius: 4rpx;
            background: linear-gradient(-90deg, rgba(255, 93, 68, 0) 0%, #ff5d44 100%);
          }
        }
      }
    }
  }
  &__content {
    position: relative;
    z-index: 1;

    .product-info {
      margin: 0 24rpx;
      margin-top: 24rpx;
      padding: 24rpx;
      background-color: #fff;
      border-radius: 24rpx;
      &__price {
        display: flex;
        align-items: flex-end;
        .sale-price {
          font-size: 26rpx;
          color: #ff5d44;
          .sale-price__num {
            font-size: 48rpx;
            font-weight: 500;
          }
        }
        /* .old-price {
          margin-left: 16rpx;
          font-size: 26rpx;
          color: #787c89;
          text-decoration: line-through;
          line-height: 32rpx;
        } */
      }
      &__title {
        font-size: 30rpx;
        color: #151d32;
        font-weight: 500;
        line-height: 42rpx;
        margin-top: 32rpx;
      }
      .tag-box {
        display: flex;
        align-items: center;
        margin-top: 18rpx;
        .product-info__tag {
          font-size: 20rpx;
          line-height: 28rpx;
          padding: 2rpx 6rpx;
          border-radius: 4rpx;
          border: 1rpx solid currentColor;
          &:not(:last-child) {
            margin-right: 16rpx;
          }
          &.fill-tag {
            background-color: #333333;
            color: #fff;
          }
          &.plain-tag {
            background-color: transparent;
            color: #ff4544;
          }
        }
      }
    }
    .card-section {
      margin: 0 24rpx;
      margin-top: 24rpx;
      padding: 24rpx;
      background-color: #fff;
      border-radius: 24rpx;
      &__title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-text {
          font-size: 30rpx;
          color: #151d32;
          line-height: 42rpx;
          font-weight: 500;
        }
      }
      &__content {
        padding-top: 20rpx;
        overflow: hidden;
      }
      &.desc {
        .card-section__content {
          padding-top: 0;
          overflow: hidden;
          height: 0;
          transition: all 0.2s ease-in-out;

          &__active {
            padding-top: 20rpx;
            height: auto;
          }
        }
      }
      &.similar-discount {
        .card-section__content {
          .goods-card-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            .goods-card-list__item {
              width: calc((100% - 32rpx) / 3);
              margin-bottom: 20rpx;
              box-sizing: border-box;
            }
          }
        }
      }
    }
  }
  &__footer {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;
    padding-bottom: calc(env(safe-area-inset-bottom) + 16rpx);
    background-color: #fff;
    box-sizing: border-box;
    .own-order {
      width: 120rpx;
      margin-right: 24rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .icon {
        width: 48rpx;
        height: 48rpx;
      }
      .text {
        margin-top: 8rpx;
        font-size: 22rpx;
        color: #3d3d3d;
        font-weight: 400;
        line-height: 30rpx;
      }
    }
    .share-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &__text {
        font-size: 32rpx;
        color: #ff5d44;
        font-weight: 500;
        line-height: 44rpx;
      }
      &__sub-text {
        font-size: 22rpx;
        color: #ff7040;
        font-weight: 400;
        line-height: 30rpx;
      }
    }
  }
}
.order-history {
  width: 150rpx;
  height: 142rpx;
  .order-history__img {
    width: 100%;
    height: 100%;
  }
}
</style>
