/*
 * @Author: ho<PERSON>og<PERSON>
 * @Date: 2025-07-05 18:30:00
 * @Description: 订单相关组合函数
 * @LastEditTime: 2025-07-16 10:08:46
 * @LastEditors: houbaoguo
 */
import type {
  CreateOrderParams,
  LoadPreOrderRes,
  PreOrderParams,
  PreOrderRes,
  UpdatePaidParams,
  WxPayRes,
} from '@/api/types/goods'
import { createOrder, loadPreOrder, preOrder, updatePaid, wxPay } from '@/api'
import { useUserStore } from '@/store'
// 预下单和加载预下单的组合结果
export interface PreOrderResult {
  preOrderData: PreOrderRes
  loadPreOrderData: LoadPreOrderRes
}

// 创建订单响应
export interface CreateOrderResult {
  orderNo: string
  wxPayParams: WxPayRes
}

export function useOrder() {
  const loading = ref(false)
  const preOrderLoading = ref(false)
  const createOrderLoading = ref(false)
  const userStore = useUserStore()

  /**
   * 预下单并加载预下单信息
   * @param params 预下单参数
   * @returns 预下单和加载预下单的结果
   */
  const handlePreOrder = async (params: PreOrderParams): Promise<PreOrderResult> => {
    preOrderLoading.value = true
    try {
      // 1. 先执行预下单
      const preOrderResponse = await preOrder(params)
      if (!preOrderResponse?.data?.preOrderNo) {
        throw new Error('预下单失败，未获取到预下单号')
      }

      const preOrderData = preOrderResponse.data

      // 2. 使用预下单号加载预下单信息
      const loadPreOrderResponse = await loadPreOrder({
        preOrderNo: preOrderData.preOrderNo,
      })

      if (!loadPreOrderResponse?.data) {
        throw new Error('加载预下单信息失败')
      }

      const loadPreOrderData = loadPreOrderResponse.data

      return {
        preOrderData,
        loadPreOrderData,
      }
    }
    catch (error) {
      console.error('预下单流程失败:', error)
      throw error
    }
    finally {
      preOrderLoading.value = false
    }
  }

  /**
   * 创建订单
   * @param params 创建订单参数
   * @returns 订单号和微信支付参数
   */
  const handleCreateOrder = async (params: CreateOrderParams): Promise<CreateOrderResult> => {
    createOrderLoading.value = true
    try {
      if (params.addressId === -1) {
        throw new Error('收货地址不能为空')
      }
      const response = await createOrder(params)

      if (!response.data?.orderNo) {
        throw new Error('创建订单失败')
      }

      const orderNo = response.data.orderNo
      // wxPay
      const res = await wxPay({
        orderNo,
        openId: userStore.wechatOpenId,
        payType: 'weixin',
        payChannel: 'routine',
      })
      const { payInfo } = res.data || {}
      const wxPayParams: WxPayRes = JSON.parse(payInfo || '{}')
      return {
        orderNo,
        wxPayParams,
      }
    }
    catch (error) {
      console.error('创建订单失败:', error)
      throw error
    }
    finally {
      createOrderLoading.value = false
    }
  }

  /**
   * 微信小程序支付
   * @param wxPayParams 微信支付参数
   */
  const handleWxPay = (wxPayParams: WxPayRes): Promise<any> => {
    return new Promise((resolve, reject) => {
      uni.requestPayment({
        provider: 'wxpay',
        orderInfo: {},
        timeStamp: wxPayParams.timeStamp,
        nonceStr: wxPayParams.nonceStr,
        package: wxPayParams.package,
        signType: wxPayParams.signType,
        paySign: wxPayParams.paySign,
        success: (res) => {
          console.log('支付成功:', res)
          resolve(res)
        },
        fail: (err) => {
          console.error('支付失败:', err)
          reject(err)
        },
      })
    })
  }

  /**
   * 更新微信支付状态
   * @param params 更新微信支付状态参数
   */
  const handleUpdatePaid = async (params: Omit<UpdatePaidParams, 'payStatus'> & Partial<Pick<UpdatePaidParams, 'payStatus'>>) => {
    try {
      const computedParams = {
        payStatus: 2 as const,
        ...params,
      }
      await updatePaid(computedParams)
    }
    catch (error) {
      console.error('更新微信支付状态失败:', error)
    }
  }

  /**
   * 完整的下单流程：预下单 -> 创建订单 -> 发起支付
   * @param preOrderParams 预下单参数
   * @param createOrderParams 创建订单参数（不包含preOrderNo，会自动填入）
   */
  const handleCompleteOrder = async (
    preOrderParams: PreOrderParams,
    createOrderParams: Omit<CreateOrderParams, 'preOrderNo'>,
  ) => {
    loading.value = true
    try {
      // 预下单并加载信息
      const preOrderResult = await handlePreOrder(preOrderParams)

      // 创建订单
      const createOrderResult = await handleCreateOrder({
        ...createOrderParams,
        preOrderNo: preOrderResult.preOrderData.preOrderNo,
      })

      // 发起微信支付
      const payResult = await handleWxPay(createOrderResult.wxPayParams)
      // 更新微信支付状态
      await handleUpdatePaid({
        orderNo: createOrderResult.orderNo,
      })

      return {
        preOrderResult,
        createOrderResult,
        payResult,
      }
    }
    catch (error) {
      console.error('完整下单流程失败:', error)
      throw error
    }
    finally {
      loading.value = false
    }
  }

  return {
    // 状态
    loading,
    preOrderLoading,
    createOrderLoading,

    // 方法
    handlePreOrder,
    handleCreateOrder,
    handleWxPay,
    handleCompleteOrder,
    handleUpdatePaid,
  }
}
