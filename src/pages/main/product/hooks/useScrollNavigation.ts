/**
 * 滚动导航组合函数
 * 用于处理页面滚动时的导航栏交互动画
 */
import type { ComponentInternalInstance } from 'vue'
import { useRect } from '@/composables/useRect'
import { computed, nextTick, ref } from 'vue'

export interface ScrollNavigationOptions {
  /** 导航栏高度，默认88rpx */
  navbarHeight?: number
  /** 透明度动画开始位置，默认0 */
  opacityStart?: number
  /** 透明度动画结束位置，默认211 */
  opacityEnd?: number
  /** 滚动偏移量，默认44 */
  scrollOffset?: number
}

export interface TabItem {
  id: string
  name: string
}

export interface SectionInfo {
  id: string
  top: number
  bottom: number
}

export function useScrollNavigation(
  tabs: TabItem[],
  options: ScrollNavigationOptions = {},
) {
  const {
    navbarHeight = uni.upx2px(88),
    opacityStart = 0,
    opacityEnd = 211,
    scrollOffset = 44,
  } = options

  // 响应式状态
  const scrollTop = ref(0)
  const opacityNum = ref(0)
  const curTabIndex = ref(0)
  const positionList = ref<SectionInfo[]>([])

  // 计算导航栏背景色
  const navBackground = computed(() => {
    if (scrollTop.value > 0) {
      return `rgba(255, 255, 255, ${opacityNum.value})`
    }
    return 'transparent'
  })

  // 创建透明度动画函数
  const createOpacityAnimation = (
    scrollStart: number,
    scrollEnd: number,
    valueStart: number,
    valueEnd: number,
  ) => {
    return (currentScrollTop: number) => {
      if (currentScrollTop <= scrollStart) {
        opacityNum.value = valueStart
        return
      }
      if (currentScrollTop >= scrollEnd) {
        opacityNum.value = valueEnd
        return
      }
      opacityNum.value
        = valueStart
          + ((valueEnd - valueStart) * (currentScrollTop - scrollStart))
          / (scrollEnd - scrollStart)
    }
  }

  // 透明度动画函数
  const updateOpacity = createOpacityAnimation(opacityStart, opacityEnd, 0, 1)

  // 计算各区块的高度和位置
  const calcSectionHeight = async (instance: ComponentInternalInstance) => {
    const sectionIds = tabs.map(tab => tab.id)

    for (let i = 0; i < sectionIds.length; i++) {
      const rect = await useRect(sectionIds[i], instance)

      if (!rect) continue

      const sectionInfo: SectionInfo = {
        id: rect.id as string,
        top: i === 0 ? navbarHeight : positionList.value[i - 1]?.bottom || 0,
        bottom: i === 0
          ? navbarHeight + (rect.height as number)
          : (positionList.value[i - 1]?.bottom || 0) + (rect.height as number),
      }

      // 更新或添加位置信息
      const existingIndex = positionList.value.findIndex(item => item.id === rect.id)
      if (existingIndex !== -1) {
        positionList.value[existingIndex] = sectionInfo
      }
      else {
        positionList.value.push(sectionInfo)
      }
    }
  }

  // 根据滚动位置计算当前标签索引
  const calcCurrentTabIndex = () => {
    const scrollTopWithOffset = scrollTop.value + scrollOffset
    const index = positionList.value.findIndex(
      item => item.top <= scrollTopWithOffset && item.bottom >= scrollTopWithOffset,
    )
    if (index !== -1) {
      curTabIndex.value = index
    }
  }

  // 滚动到指定位置
  const scrollToSection = (sectionId: string) => {
    // 如果是第一个标签，滚动到顶部
    if (sectionId === tabs[0]?.id) {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      })
      return
    }

    // 找到目标区块的位置
    const targetSection = positionList.value.find(item => item.id === sectionId)
    if (targetSection) {
      uni.pageScrollTo({
        scrollTop: targetSection.top - scrollOffset + 5,
        duration: 300,
      })
    }
  }

  // 处理页面滚动事件
  const handlePageScroll = (e: { scrollTop: number }) => {
    scrollTop.value = e.scrollTop
    updateOpacity(scrollTop.value)
    calcCurrentTabIndex()
  }

  // 初始化函数
  const initScrollNavigation = async (instance: ComponentInternalInstance) => {
    await calcSectionHeight(instance)
    calcCurrentTabIndex()
  }

  // 重新计算（用于内容变化后）
  const recalculate = async (instance: ComponentInternalInstance, delay = 300) => {
    setTimeout(() => {
      nextTick(async () => {
        await calcSectionHeight(instance)
        calcCurrentTabIndex()
      })
    }, delay)
  }

  return {
    // 响应式状态
    scrollTop: readonly(scrollTop),
    opacityNum: readonly(opacityNum),
    curTabIndex: readonly(curTabIndex),
    positionList: readonly(positionList),

    // 计算属性
    navBackground,

    // 方法
    handlePageScroll,
    scrollToSection,
    initScrollNavigation,
    recalculate,
    calcSectionHeight,
    calcCurrentTabIndex,
  }
}
