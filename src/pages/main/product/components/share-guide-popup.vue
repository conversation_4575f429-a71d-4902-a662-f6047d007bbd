<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-06-20 11:03:23
 * @Description:
 * @LastEditTime: 2025-07-15 20:50:42
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
const visible = defineModel<boolean>('visible', {
  required: true,
})
const swiperRef = ref<any>(null)

watch(visible, (val) => {
  if (val) {
    nextTick(() => {
      swiperRef.value.init()
    })
  }
})
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <fx-popup
    v-model:visible="visible"

    position="bottom"
  >
    <view class="popup-content">
      <image class="popup-content-bg" src="@img/product/share-guide-bg.png" />
      <view class="popup-content-title">
        <text class="popup-content-title-text">
          限时分享赚奖励
        </text>
        <image
          class="popup-content-title-close-icon"
          h-48rpx
          w-48rpx
          src="@img/common/close-icon.png"
          @click="visible = false"
        />
      </view>
      <view class="popup-content-body">
        <view class="popup-content-body-desc-title">
          温馨提示
        </view>
        <view class="popup-content-body-desc-text">
          仅在 App 内分享才能获得奖励。限时推广活动火热进行中，立即前往纷享生活App参与，赚取专属收益！
        </view>

        <view class="swiper-container">
          <fx-swiper
            ref="swiperRef"
            width="600"
            height="470"
            touchable
            pagination-visible
            pagination-color="#FF7344"
            auto-play="1500"
          >
            <fx-swiper-item>
              <view class="swiper-item">
                <image class="swiper-item-img" src="@img/product/guide-swiper-item-1.png" />
              </view>
            </fx-swiper-item>
            <fx-swiper-item>
              <view class="swiper-item">
                <image class="swiper-item-img" src="@img/product/guide-swiper-item-2.png" />
              </view>
            </fx-swiper-item>
            <fx-swiper-item>
              <view class="swiper-item">
                <image class="swiper-item-img" src="@img/product/guide-swiper-item-3.png" />
              </view>
            </fx-swiper-item>
          </fx-swiper>
        </view>
      </view>
      <view class="popup-content-footer">
        <fx-button

          radius="32rpx"

          ml-24rpx
          mr-24rpx
          block
          :custom-style="{ background: 'linear-gradient( -90deg, #FF8C3B 0%, #FF3F3C 100%);', color: '#fff', height: '100rpx', fontSize: '36rpx' }"
          @click="visible = false"
        >
          知道了
        </fx-button>
      </view>
    </view>
  </fx-popup>
</template>

<style scoped lang="scss">
::v-deep .fx-popup {
  background-color: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}
.popup-content {
  position: relative;
  width: 100vw;
  background-color: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 24rpx;
  box-sizing: border-box;
  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 230rpx;
  }
  &-title {
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    &-text {
      font-size: 36rpx;
      font-weight: 500;
      color: #151d32;
      line-height: 50rpx;
    }
    &-close-icon {
      position: absolute;
      right: 0;
    }
  }
  &-body {
    width: 100%;
    height: 100%;
    &-desc-title {
      margin-top: 32rpx;
      padding: 0 24rpx;
      font-size: 30rpx;
      font-weight: 500;
      color: #151d32;
      line-height: 42rpx;
    }
    &-desc-text {
      position: relative;
      margin: 16rpx 24rpx 0;
      font-size: 30rpx;
      font-weight: 400;
      color: #454a5b;
      line-height: 42rpx;
    }
    .swiper-container {
      width: 600rpx;
      height: 470rpx;
      margin: 32rpx auto 0;
      // border-radius: 12rpx;
      // overflow: hidden;

      .swiper-item {
        width: 100%;
        height: 100%;
        .swiper-item-img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  &-footer {
    margin-top: 80rpx;
  }
}
</style>
