<!--
 * @Author: houbaoguo
 * @Date: 2025-06-24 15:39:08
 * @Description:
 * @LastEditTime: 2025-07-08 18:30:25
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { HotGoodsListResItem } from '@/api/types/goods'

interface Props {
  item: HotGoodsListResItem
}

defineProps<Props>()
const emit = defineEmits<{
  (e: 'itemTap'): void
  (e: 'btnTap'): void
}>()
</script>

<template>
  <view class="goods-list-item" @tap="emit('itemTap')">
    <view class="goods-list-item__img">
      <image
        class="goods-list-item__img-img"
        :src="item.image"
        mode="aspectFill"
      />
    </view>
    <view class="goods-list-item__right">
      <view class="title-tag-wrap">
        <view class="title ellipsis-2-lines">
          {{ item.storeName }}
        </view>
        <view v-if="item.cateIds && item.cateIds.length" class="tag-box">
          <view v-if="(item.cateIds || []).includes('PRE_SELL')" class="tag fill-tag">
            预售
          </view>
          <view v-if="(item.cateIds || []).includes('FREE_SHIPPING')" class="tag plain-tag">
            包邮
          </view>
          <view v-if="(item.cateIds || []).includes('HOT_PRODUCT')" class="tag plain-tag">
            热卖产品
          </view>
        </view>
      </view>

      <view class="price-share-btn">
        <view class="price">
          <view class="old-price">
            <fx-amount
              show-line-through
              show-symbol
              symbol-font-weight="400"
              :amount="item.otPrice"
              size="26rpx"
              color="#787C89"
            />
          </view>
          <view class="sale-price">
            <text>到手价</text>
            <fx-amount
              show-symbol
              mb--2rpx
              ml-6rpx
              symbol-size="28rpx"
              symbol-font-weight="500"
              :amount="item.price"
              size="40rpx"
              color="#FF531A"
              font-weight="700"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.goods-list-item {
  display: flex;
  // align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.04);
  &__img {
    width: 216rpx;
    height: 216rpx;
    border-radius: 16rpx;
    &-img {
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
    }
  }
  &__right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    margin-left: 16rpx;
    .title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
      line-height: 40rpx;
    }
    .tag-box {
      display: flex;
      align-items: center;
      margin-top: 10rpx;
      .tag {
        font-size: 20rpx;
        line-height: 28rpx;
        padding: 2rpx 6rpx;
        border-radius: 4rpx;
        border: 1rpx solid currentColor;
        &:not(:last-child) {
          margin-right: 16rpx;
        }
        &.fill-tag {
          background-color: #333333;
          color: #fff;
        }
        &.plain-tag {
          background-color: transparent;
          color: #ff4544;
        }
      }
    }
    .price-share-btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20rpx;
      .price {
        .sale-price {
          display: flex;
          align-items: flex-end;
          font-size: 24rpx;
          color: #ff531a;
          font-weight: 500;
        }
      }
      .share-btn {
        &__img {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
      }
    }
  }
}
</style>
