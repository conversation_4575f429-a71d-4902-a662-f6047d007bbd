<!--
 * @Author: houbaoguo
 * @Date: 2025-06-20 11:03:23
 * @Description:
 * @LastEditTime: 2025-07-31 17:07:28
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { CreateOrderParams, EditAddressParams } from '@/api/types/goods'
import { editAddress, getAddressList } from '@/api'

const props = withDefaults(defineProps<{
  attrInfo: Record<string, any>
  totalPayFee: string
  freightFee: string
  preOrderLoading: boolean
  completeOrderLoading: boolean
  promoterId: string
}>(), {
  attrInfo: () => ({
    productAttr: [],
    productSelect: {},
  }),
  totalPayFee: '0',
  freightFee: '--',
  preOrderLoading: false,
  completeOrderLoading: false,
  promoterId: '',
})

const emit = defineEmits<{
  (e: 'changeAttr', attr: Record<string, any>): void
  (e: 'confirmOrder', params: CustomCreateOrderParams): void
  (e: 'changeAddressId', addressId: number): void
}>()
type CustomCreateOrderParams = Omit<CreateOrderParams, 'preOrderNo'>
const { toast } = useToast()
const visible = defineModel<boolean>('visible', {
  required: true,
})
const productNum = defineModel<number>('productNum', {
  required: true,
})

// 延迟显示loading状态，避免闪烁
const showDelayedLoading = ref(false)
let loadingTimer: NodeJS.Timeout | null = null
// 编辑地址loading
const mark = ref('')
const address = reactive({
  id: -1,
  userName: '',
  telNumber: '',
  detailInfoNew: '',
})
// 监听preOrderLoading变化，实现延迟显示
watch(() => props.preOrderLoading, (newVal) => {
  if (newVal) {
    // 开始loading，设置300ms延迟
    loadingTimer = setTimeout(() => {
      showDelayedLoading.value = true
    }, 300)
  }
  else {
    // 结束loading，立即隐藏并清除定时器
    if (loadingTimer) {
      clearTimeout(loadingTimer)
      loadingTimer = null
    }
    showDelayedLoading.value = false
  }
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (loadingTimer) {
    clearTimeout(loadingTimer)
  }
})

// 获取收货地址列表
async function getAddressListHandler() {
  const res = await getAddressList({
    source: 'wechat',
    page: 1,
    limit: 1,
  })
  const { id, realName, detail, phone } = res.data?.list?.[0] || {}
  console.log('🚀 ~ getAddressListHandler ~ id:', id)
  address.id = id || -1
  address.userName = realName
  address.telNumber = phone
  address.detailInfoNew = detail
}

// 编辑订单地址
async function handleEditAddress(params: EditAddressParams) {
  try {
    const res = await editAddress(params)
    if (!res.data?.id) {
      throw new Error('编辑订单地址失败')
    }
    address.id = res.data.id
  }
  catch (error) {
    console.error('编辑订单地址失败:', error)
    throw error
  }
}

// 选择规格
function handleSelectAttr(index: number, cIndex: number) {
  console.log('🚀 ~ handleSelectAttr ~ index:', index)
  console.log('🚀 ~ handleSelectAttr ~ cIndex:', cIndex)
  emit('changeAttr', {
    index,
    cIndex,
  })
}

function handleAddress() {
  uni.chooseAddress({
    success: async (res) => {
      console.log('🚀 ~ handleAddress ~ res:', res)
      // 🚀 ~ handleAddress ~ res: {"nationalCodeFull": "310109014", "telNumber": "18071409973", "userName": "侯先生", "nationalCode": "310109", "errMsg": "chooseAddress:ok", "postalCode": "200080", "provinceName": "上海市", "cityName": "上海市", "countyName": "虹口区", "streetName": "嘉兴路街道", "detailInfoNew": "飞虹路瑞虹企业天地瑞虹企业天地1号楼21楼", "detailInfo": "嘉兴路街道飞虹路瑞虹企业天地瑞虹企业天地1号楼21楼"}
      uni.showLoading({
        title: '请稍后',
        mask: true,
      })
      await handleEditAddress({
        source: 'wechat',
        realName: res.userName,
        phone: res.telNumber,
        detail: res.detailInfoNew as string,
        id: 0,
        address: {
          province: res.provinceName,
          city: res.cityName,
          district: res.countyName,
          // cityId: res.cityId,
        },
        isDefault: false,
      })
      address.userName = res.userName
      address.telNumber = res.telNumber
      address.detailInfoNew = res.detailInfoNew as string
      uni.hideLoading()
    },
    fail: (err) => {
      console.log(err)
    },
  })
}

// 确认订单
async function confirmOrder() {
  // 库存不足提示
  if (props.attrInfo.productAttr.length && props.attrInfo.productSelect.stock === 0) {
    toast('产品库存不足，请选择其它')
    return
  }
  // 地址不能为空
  if (address.id === -1) {
    toast('请先选择收货地址')
    return
  }
  if (props.completeOrderLoading) {
    return
  }
  // 确认订单
  console.log('🚀 ~ confirmOrder ~ attrInfo:', props.attrInfo)
  const params: CustomCreateOrderParams = {
    realName: address.userName,
    phone: address.telNumber,
    shippingType: 1,
    couponId: 0,
    payType: 'weixin',
    payChannel: 'routine',
    useIntegral: false,
    mark: mark.value,
    promoterId: props.promoterId,
    addressId: address.id,
  }
  emit('confirmOrder', params)
}

// 事件代理，统一拦截在loading时点击其他按钮
function proxyHandler(event: () => void) {
  if (props.preOrderLoading) return
  if (props.completeOrderLoading) return
  if (typeof event === 'function')
    event()
}

// 同步收货地址id到父组件
watch(() => address.id, (newVal) => {
  console.log('🚀 ~ watch ~ newVal:', newVal)
  emit('changeAddressId', newVal)
})

defineExpose({
  getAddressListHandler,
})
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <fx-popup
    v-model:visible="visible"
    position="bottom"
    safe-area-inset-bottom
    :mask-closeable="false"
  >
    <view class="popup-content">
      <view class="popup-content-title">
        <image
          class="popup-content-title-close-icon"
          h-48rpx
          w-48rpx
          src="@img/common/close-icon.png"
          @tap="proxyHandler(() => visible = false)"
        />
        <text class="popup-content-title-text">
          确认订单
        </text>
      </view>
      <view class="popup-content-body">
        <!-- 收货地址 -->
        <view class="order-address" @tap="proxyHandler(handleAddress)">
          <image
            class="address-icon"
            h-48rpx
            w-48rpx
            src="@img/product/map-pin.png"
            mode="widthFix"
          />
          <view class="address-title">
            <text>{{ address.detailInfoNew || '设置收货地址' }}</text>
            <text class="name-tel">
              {{ address.userName }} {{ address.telNumber }}
            </text>
          </view>
          <image
            class="arrow-right"
            h-48rpx
            w-48rpx
            src="@img/product/arrow-right.png"
            mode="widthFix"
          />
        </view>
        <!-- 商品sku -->
        <view class="order-sku">
          <scroll-view scroll-y class="scroll-view">
            <view class="scroll-view-content">
              <view class="order-info">
                <view class="order-thumb">
                  <image
                    h-full
                    w-full
                    :src="attrInfo.productSelect.image"
                    mode="aspectFill"
                  />
                </view>
                <view class="order-info-content">
                  <view class="order-info-title ellipsis-2-lines">
                    <text>{{ attrInfo.productSelect.storeName }}</text>
                  </view>
                  <view class="order-info-price">
                    <fx-amount
                      show-symbol
                      symbol-size="36rpx"
                      font-weight="500"
                      :amount="attrInfo.productSelect.price"
                      color="#FF5406"
                      size="36rpx"
                    />
                  </view>
                </view>
              </view>
              <!-- 规格 -->
              <view
                v-for="(item, index) in attrInfo.productAttr"
                :key="index"
                class="order-sku-spec"
              >
                <text class="order-sku-spec-unit">
                  {{ item.attrName }}
                </text>
                <view class="order-sku-spec-item-list">
                  <view
                    v-for="(attr, cIndex) in item.attrValues"
                    :key="cIndex"
                    class="order-sku-spec-item"
                    :class="{ active: item.index === attr }"
                    @tap="proxyHandler(() => handleSelectAttr(index, cIndex))"
                  >
                    <text>{{ attr }}</text>
                  </view>
                </view>
              </view>
              <!-- cell-list -->
              <view class="order-sku-cell-list">
                <view class="order-sku-cell-item">
                  <text class="label">
                    数量
                  </text>
                  <view class="value">
                    <fx-input-number v-model="productNum" :disabled="preOrderLoading || completeOrderLoading">
                      <template #leftIcon>
                        <view class="icon-subtract">
                          <view
                            class="i-carbon:subtract"
                            h-full
                            w-full
                          />
                        </view>
                      </template>
                      <template #rightIcon>
                        <view class="icon-add">
                          <view
                            class="i-carbon:add"
                            h-full
                            w-full
                          />
                        </view>
                      </template>
                    </fx-input-number>
                  </view>
                </view>
                <view class="order-sku-cell-item">
                  <text class="label">
                    配送
                  </text>
                  <view v-if="freightFee !== '--'" class="value">
                    <text v-if="['0', '0.00'].includes(freightFee)">
                      免运费
                    </text>
                    <fx-amount
                      v-else
                      show-symbol
                      symbol-font-weight="400"
                      :amount="freightFee"
                      size="28rpx"
                    />
                  </view>
                </view>
                <!-- <view class="order-sku-cell-item">
                  <text class="label">
                    优惠
                  </text>
                  <view class="value">
                    暂无可用优惠
                  </view>
                </view> -->
                <view class="order-sku-cell-item">
                  <text class="label">
                    备注
                  </text>
                  <view class="value">
                    <fx-input
                      v-model="mark"
                      placeholder="选填"
                      :disabled="preOrderLoading || completeOrderLoading"
                      :maxlength="150"
                    />
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <view class="popup-content-footer">
        <view class="total-amount">
          <text>合计：</text>
          <text
            v-if="showDelayedLoading && !completeOrderLoading"
            class="loader-FF531A"
            text-sm
          />
          <fx-amount
            v-else
            show-symbol
            symbol-size="32rpx"
            symbol-font-weight="400"
            :amount="totalPayFee"
            size="44rpx"
            color="#FF531A"
            font-weight="700"
          />
        </view>
        <fx-button
          radius="50px"
          :custom-style="{ background: 'linear-gradient( -90deg, #FF3433 0%, #FF8133 100%)', color: '#fff', width: '208rpx', height: '72rpx', fontSize: '32rpx', lineHeight: '44rpx', border: 'none' }"
          @tap="confirmOrder"
        >
          <text
            v-if="showDelayedLoading || completeOrderLoading"
            class="loader-fff"
            text-sm
          />
          <text v-else>
            立即支付
          </text>
        </fx-button>
      </view>
    </view>
  </fx-popup>
</template>

  <style scoped lang="scss">
  ::v-deep .fx-popup {
  background-color: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
}
.popup-content {
  position: relative;
  width: 100vw;
  background-color: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  padding: 24rpx 24rpx 0;
  box-sizing: border-box;
  &-title {
    display: flex;
    justify-content: center;
    align-items: center;
    &-text {
      font-size: 32rpx;
      font-weight: 500;
      color: #151d32;
      line-height: 44rpx;
    }
    &-close-icon {
      position: absolute;
      left: 24rpx;
    }
  }
  &-body {
    width: 100%;
    height: 100%;
    .order-address {
      position: relative;
      display: flex;
      align-items: center;
      padding: 26rpx 8rpx;
      margin-top: 22rpx;
      margin-bottom: 16rpx;
      &::after {
        content: '';
        position: absolute;
        bottom: -16rpx;
        left: -24rpx;
        right: -24rpx;
        height: 16rpx;
        background-color: #f2f2f2;
      }
      .address-icon {
        align-self: flex-start;
      }
      .address-title {
        display: flex;
        flex-direction: column;
        margin-left: 8rpx;
        font-size: 32rpx;
        font-weight: 400;
        color: #151d32;
        .name-tel {
          margin-top: 4rpx;
          font-size: 28rpx;
          font-weight: 400;
          color: #787c89;
          line-height: 40rpx;
        }
      }
      .arrow-right {
        margin-left: auto;
      }
    }
    .order-sku {
      padding: 32rpx 0;
      // overflow-y: auto;
      // box-sizing: border-box;
      .scroll-view {
        width: 100vw;
        max-height: 55vh;
        margin-left: -32rpx;
        margin-right: -32rpx;
        .scroll-view-content {
          padding: 0 32rpx;
          box-sizing: border-box;
        }
      }
      .order-info {
        display: flex;
        .order-thumb {
          width: 160rpx;
          height: 160rpx;
          overflow: hidden;
          border-radius: 8rpx;
          border: 1rpx solid #e5e5e5;
        }
        .order-info-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 24rpx;
          .order-info-title {
            font-size: 28rpx;
            font-weight: 400;
            color: #151d32;
            line-height: 40rpx;
          }
        }
      }
      &-spec {
        margin-top: 32rpx;
        &-unit {
          font-size: 28rpx;
          font-weight: 400;
          color: #9b9b9b;
          line-height: 40rpx;
        }
        &-item-list {
          display: flex;
          // overflow-x: auto;
          flex-wrap: wrap;
          .order-sku-spec-item {
            margin-top: 16rpx;
            padding: 8rpx 32rpx;
            border-radius: 50px;
            background: #f2f2f2;
            font-size: 28rpx;
            color: #282828;
            line-height: 40rpx;
            font-weight: 400;
            border: 2rpx solid #f2f2f2;
            box-sizing: border-box;
            white-space: nowrap;
            &.active {
              background: #fff6f3;
              color: #ff5406;
              border-color: #ff5406;
            }
            &:not(:last-child) {
              margin-right: 16rpx;
            }
          }
        }
      }
      &-cell-list {
        padding: 16rpx 0;
        .order-sku-cell-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16rpx 0;
          .label {
            width: 60rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #9b9b9b;
            line-height: 40rpx;
          }
          .value {
            flex: 1;
            font-size: 28rpx;
            font-weight: 400;
            color: #151d32;
            line-height: 40rpx;
            text-align: right;
            --fx-inputnumber-height: 40rpx;
            --fx-inputnumber-line-height: 40rpx;
            --fx-inputnumber-input-width: 60rpx;
            --fx-inputnumber-input-margin: 0 24rpx;
            --fx-inputnumber-input-font-size: 28rpx;
            .icon-subtract,
            .icon-add {
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #f1f2f2;
              width: 32rpx;
              height: 32rpx;
              border-radius: 50%;
              font-size: 16rpx;
              color: #9e9e9e;
              -webkit-tap-highlight-color: transparent;
              font-weight: 500;
            }
            .fx-input-number__icon--disabled {
              .icon-subtract,
              .icon-add {
                color: #ccc;
              }
            }
          }
        }
      }
    }
  }
  &-footer {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 8rpx;
    box-sizing: border-box;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -24rpx;
      right: -24rpx;
      height: 1rpx;
      background-color: #f2f2f2;
    }
    .total-amount {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      font-weight: 400;
      color: #151d32;
      line-height: 42rpx;
      .amount {
        font-size: 44rpx;
        font-weight: 700;
        color: #ff5406;
        line-height: 48rpx;
        font-family: 'DIN-Bold';
      }
    }
  }
}
</style>
