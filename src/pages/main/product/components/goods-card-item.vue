<!--
 * @Author: houbaoguo
 * @Date: 2025-06-18 14:34:20
 * @Description:
 * @LastEditTime: 2025-07-08 18:26:03
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import { formatAmount } from '@/utils'

defineProps<{
  item: any
}>()
const emit = defineEmits<{
  (e: 'btnTap'): void
  (e: 'cardTap'): void
}>()
</script>

<template>
  <view class="goods-card-item" @tap="emit('cardTap')">
    <view class="goods-card-item__img">
      <image
        class="goods-card-item__img-img"
        :src="item.image"
        mode="aspectFill"
      />
    </view>
    <view class="goods-card-item__title ellipsis-1-lines">
      {{ item.storeName }}
    </view>
    <view class="goods-card-item__price">
      <text>到手价</text>
      <fx-amount
        show-symbol
        symbol-size="24rpx"
        symbol-font-weight="500"
        :amount="item.price"
        size="36rpx"
        color="#ff531a"
        font-weight="700"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.goods-card-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  &__img {
    width: 100%;
    height: 208rpx;
    border-radius: 16rpx;
    overflow: hidden;
    border: 1rpx solid #e5e5e5;
    box-sizing: border-box;
    &-img {
      width: 100%;
      height: 100%;
    }
  }
  &__title {
    width: 100%;
    font-size: 28rpx;
    color: #333;
    font-weight: 600;
    line-height: 40rpx;
    margin-top: 16rpx;
  }
  &__price {
    width: 100%;
    font-size: 22rpx;
    color: #ff531a;
    margin-top: 12rpx;
    line-height: 34rpx;
    &-currency {
      font-size: 24rpx;
      line-height: 30rpx;
      margin-bottom: -4rpx;
      margin-left: -4rpx;
      font-family: 'DIN-Bold';
    }
  }
  &__btn {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 52rpx;
    border-radius: 10rpx;
    overflow: hidden;
    background-image: url('@img/product/share-btn-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    color: #fff;
    font-size: 22rpx;
    line-height: 40rpx;
    margin-top: 12rpx;
    .btn-content {
      max-width: 124rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      padding-left: 12rpx;
    }
  }
}
</style>
