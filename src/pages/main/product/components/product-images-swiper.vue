<!--
 * @Author: houbaoguo
 * @Date: 2025-07-04 13:25:37
 * @Description:
 * @LastEditTime: 2025-07-11 14:01:11
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
defineProps<{
  swiperList: string[]
}>()
const emit = defineEmits<{
  (e: 'imagePreview', index: number): void
}>()
const activeIndex = ref(0)
function handleSwiperChange(event: any) {
  activeIndex.value = event.detail.current
}

const PREVIOUS_MARGIN = 12
const previousMargin = computed(() => {
  return `${PREVIOUS_MARGIN}px`
})

const nextMargin = computed(() => {
  const screenWidth = uni.getSystemInfoSync().screenWidth
  const swiperItemWidth = uni.upx2px(320)
  const NEXT_MARGIN = screenWidth - swiperItemWidth - PREVIOUS_MARGIN
  console.log('NEXT_MARGIN:', NEXT_MARGIN)
  return `${NEXT_MARGIN}px`
})
</script>

<template>
  <view class="swiper-container">
    <swiper
      h-320rpx
      :previous-margin="previousMargin"
      :next-margin="nextMargin"
      @change="handleSwiperChange"
    >
      <swiper-item
        v-for="(item, index) in swiperList"
        :key="item"
      >
        <view
          class="swiper-item__img-box"
          :class="{ 'swiper-item__img-box__active': activeIndex === index }"
          @tap="emit('imagePreview', index)"
        >
          <image
            :src="item"
            class="swiper-item__img"
            mode="aspectFill"
          />
        </view>
      </swiper-item>
    </swiper>
    <view class="page-indicator">
      <text class="current-page">
        {{ activeIndex + 1 }}
      </text>
      <text class="total-page">
        /{{ swiperList.length }}
      </text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.swiper-container {
  position: relative;
  margin-top: 24rpx;
  padding-bottom: 66rpx;
  .swiper-item {
    &__img-box {
      position: relative;
      width: 100%;
      height: 100%;
      border-radius: 16rpx;
      overflow: hidden;
      // margin-left: 24rpx;
      &__active {
        .swiper-item__img {
          width: 100%;
          height: 100%;
          transition: all ease-in-out 0.1s;
        }
      }
    }
    &__img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: calc(100% - 20px);
      height: calc(100% - 20px);
      border-radius: 16rpx;
    }
  }
  .page-indicator {
    position: absolute;
    bottom: 0rpx;
    left: 50%;
    transform: translateX(-50%);
    display: inline-flex;
    margin-top: 24rpx;
    align-items: center;
    justify-content: center;
    line-height: 34rpx;
    padding: 3rpx 24rpx;
    border-radius: 40rpx;
    background-color: #fff;
    .current-page {
      font-size: 24rpx;
      color: #ff4544;
    }
    .total-page {
      font-size: 24rpx;
      color: #787c89;
    }
  }
}
</style>
