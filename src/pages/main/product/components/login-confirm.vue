<!--
 * @Author: houbaoguo
 * @Date: 2025-06-16 18:07:55
 * @Description:
 * @LastEditTime: 2025-07-04 18:03:01
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import { type AgreementListRes, AgreementTypes, LoginTypes } from '@/api/types'
import useAgreement from '@/composables/useAgreement'
import { useAgreementStore, useUserStore } from '@/store'

const props = defineProps<{
  marketingGroupNo: string
}>()
const emit = defineEmits<{
  (e: 'success'): void
}>()
const agreementStore = useAgreementStore()
const userStore = useUserStore()
const { toAgreement } = useRouter()
const visible = defineModel<boolean>('visible', { required: true })
const { agreementName: serviceAgreementName } = useAgreement(AgreementTypes.FLUSA)
const { agreementName: privacyAgreementName } = useAgreement(AgreementTypes.FLPP)
interface GetPhoneNumberEvent {
  detail: {
    code: string
    errMsg: string
  }
}

const faceAgreementNos = computed(() => {
  return agreementStore?.agreementNameList.filter((item: AgreementListRes) => [AgreementTypes.FLUSA, AgreementTypes.FLPP].includes(item.agreementType as any)).map((item: AgreementListRes) => item.id) as number[]
})

// 保存协议状态
function saveAgreementStatus(mobile: string) {
  agreementStore.setAgreementStatusWithoutLogin({
    mobile,
    faceAgreementNos: faceAgreementNos.value,
    recodeType: 6,
  })
}

// 获取手机号并登录
async function getrealtimephonenumber(e: GetPhoneNumberEvent) {
  console.log('getrealtimephonenumber', e)
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    uni.showLoading({
      mask: true,
    })
    try {
      // 如果未获取微信授权信息，获取微信授权信息
      if (!userStore.wechatOpenId) {
        await userStore.getWechatAuthInfo()
      }
      await userStore.loginHandle({
        wechatCode: e.detail.code,
        marketingGroupNo: props.marketingGroupNo,
      })
      // 保存协议状态
      saveAgreementStatus(userStore.mobile)
      visible.value = false
      emit('success')
    }
    catch (error) {
      console.log('error', error)
    }
    finally {
      uni.hideLoading()
    }
  }
}
</script>

<template>
  <fx-confirm
    v-model:fx-confirm-visible="visible"
    title="服务协议和隐私政策"
    confirm-text="同意并登录"
    cancel-text="不同意"
    is-btn-mode
  >
    <template #content>
      <view v-if="serviceAgreementName && privacyAgreementName" class="login-page__agreement-content">
        注册纷享生活app进入下一步，请阅读并同意纷享生活
        <text class="login-page__agreement-text" @click="toAgreement(AgreementTypes.FLUSA)">
          《{{ serviceAgreementName }}》
        </text>
        <text class="login-page__agreement-text" @click="toAgreement(AgreementTypes.FLPP)">
          《{{ privacyAgreementName }}》
        </text>
      </view>
    </template>
    <template #footer>
      <view class="login-page__agreement-footer">
        <fx-button
          class="btn-cancel"
          :radius="24"
          block
          w-full
          custom-style="background: rgba(56, 123, 255, 0.12);color: #387BFF;height: 96rpx;"
          @click="visible = false"
        >
          不同意
        </fx-button>
        <fx-button
          type="primary"
          class="btn-confirm"
          :radius="24"
          block
          w-full
          custom-style="background: #387BFF;color: #fff;height: 96rpx;"
          open-type="getRealtimePhoneNumber"
          @getrealtimephonenumber="getrealtimephonenumber"
        >
          同意并登录
        </fx-button>
      </view>
    </template>
  </fx-confirm>
</template>

<style scoped lang="scss">
.login-page__agreement-content {
  .login-page__agreement-text {
    color: #387bff;
  }
}
.login-page__agreement-footer {
  width: 100%;
  display: flex;
  gap: 16rpx;
  font-weight: 500 !important;
}
</style>
