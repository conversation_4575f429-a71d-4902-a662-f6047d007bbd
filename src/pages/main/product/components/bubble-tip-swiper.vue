<!--
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2025-06-19 17:29:41
 * @Description: 电商平台产品详情页左上角提示气泡组件
 * @LastEditTime: 2025-07-08 11:38:14
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
interface Props {
  list: string[]
  interval?: number // 切换间隔时间，默认3000ms
  animationDuration?: number // 动画持续时间，默认500ms
}

const props = withDefaults(defineProps<Props>(), {
  interval: 3000,
  animationDuration: 300,
})

const currentIndex = ref(0)
const isAnimating = ref(false)
const timer = ref<NodeJS.Timeout | null>(null)

// 当前显示的文案
const currentText = ref('')
// 下一个要显示的文案
const nextText = ref('')

// 初始化
watch(props, () => {
  console.log('props.list', props.list)
  if (props.list && props.list.length > 0) {
    currentText.value = props.list[0]
    startAutoPlay()
  }
}, { immediate: true })

// 清理定时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
})

// 开始自动播放
function startAutoPlay() {
  if (props.list.length <= 1) return

  timer.value = setInterval(() => {
    switchToNext()
  }, props.interval)
}

// 切换到下一个
function switchToNext() {
  if (isAnimating.value || props.list.length <= 1) return

  isAnimating.value = true

  // 计算下一个索引
  const nextIndex = (currentIndex.value + 1) % props.list.length
  nextText.value = props.list[nextIndex]

  // 开始动画
  setTimeout(() => {
    currentText.value = nextText.value
    currentIndex.value = nextIndex
    isAnimating.value = false
  }, props.animationDuration)
}
</script>

<template>
  <view v-if="list && list.length > 0" class="bubble-tip-swiper">
    <!-- 当前显示的文案 -->
    <view
      class="bubble-tip-item current"
      :class="{ 'slide-out': isAnimating }"
      :style="{
        animationDuration: `${animationDuration}ms`,
        transitionDuration: `${animationDuration}ms`,
      }"
    >
      <view class="bubble-tip-text">
        {{ currentText }}
      </view>
    </view>

    <!-- 下一个要显示的文案 -->
    <view
      v-if="isAnimating"
      class="bubble-tip-item next"
      :class="{ 'slide-in': isAnimating }"
      :style="{
        animationDuration: `${animationDuration}ms`,
        transitionDuration: `${animationDuration}ms`,
      }"
    >
      <view class="bubble-tip-text">
        {{ nextText }}
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.bubble-tip-swiper {
  position: relative;
  width: auto;
  height: 124rpx;
  overflow: hidden;
}

.bubble-tip-item {
  position: absolute;
  bottom: 0;
  left: 0;
  width: auto;
  height: 62rpx;
  // 添加裁剪路径，只在可视区域内显示内容，但不影响动画
  // clip-path: inset(0 0 0 0);

  &.current {
    z-index: 2;

    &.slide-out {
      animation: slideOutUp 0.15s ease-in-out forwards;
    }
  }

  &.next {
    z-index: 1;
    transform: translateY(100%);
    opacity: 0;

    &.slide-in {
      animation: slideInUp 0.3s ease-in-out forwards;
    }
  }
}

.bubble-tip-text {
  width: auto;
  height: 58rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50px;
  font-size: 24rpx;
  color: #fff;
  padding: 0 24rpx;
  box-sizing: border-box;
  line-height: 58rpx;
  text-align: left;
  white-space: nowrap;
  margin-top: 2rpx;
}

// 向上滑出
@keyframes slideOutUp {
  0% {
    transform: translateY(0);
    opacity: 1;
  }
  70% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}

// 向上滑入
@keyframes slideInUp {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  30% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
