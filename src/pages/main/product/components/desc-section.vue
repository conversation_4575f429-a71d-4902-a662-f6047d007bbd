<script setup lang="ts">
const emit = defineEmits<{
  (e: 'toggleChange'): void
}>()
// 价格说明
const togglePriceDesc = ref(false)
// 发货时效说明
const toggleDeliveryDesc = ref(false)

// 展开收起
function toggleDescChange(type: 'price' | 'delivery') {
  if (type === 'price') {
    togglePriceDesc.value = !togglePriceDesc.value
  }
  else {
    toggleDeliveryDesc.value = !toggleDeliveryDesc.value
  }
  emit('toggleChange')
}
</script>

<template>
  <view class="toggle__title" @tap="toggleDescChange('price')">
    <view class="toggle__title-text">
      价格说明
    </view>
    <image v-if="!togglePriceDesc" w-40rpx h-40rpx src="@img/common/arrow-right.png" />
    <image v-else w-40rpx h-40rpx rotate-90 src="@img/common/arrow-right.png" />
  </view>
  <view class="desc-content" :class="{ active: togglePriceDesc }">
    <view class="desc-content-tip">
      <text class="pt_dian">
        •
      </text>
      <text class="pt_text">
        划线价格
      </text>
    </view>
    <view class="desc-content-text">
      商品的划线价为官方指导价、专柜价、吊牌价、正品零售价、或该商品曾经展示过的销售价等，并非原价；划线价由于地区、时间的差异和市场行情的波动，可能与您购物时展示的不一致，该价格仅供您参考。
    </view>
    <view class="desc-content-tip">
      <text class="pt_dian">
        •
      </text>
      <text class="pt_text">
        非划线价格
      </text>
    </view>
    <view class="desc-content-text">
      商品的实时销售价。具体成交价格根据商品参加活动、用户使用优惠券/积分等产生变化，最终以订单结算页价格为准。
    </view>
  </view>

  <view class="toggle__title" @tap="toggleDescChange('delivery')">
    <view class="toggle__title-text">
      发货时效说明
    </view>
    <image v-if="!toggleDeliveryDesc" w-40rpx h-40rpx src="@img/common/arrow-right.png" />
    <image v-else w-40rpx h-40rpx rotate-90 src="@img/common/arrow-right.png" />
  </view>
  <view class="desc-content" :class="{ active: toggleDeliveryDesc }">
    <view class="desc-content-tip">
      <text class="pt_dian">
        •
      </text>
      <text class="pt_text">
        特惠商城配送费用
      </text>
    </view>
    <view class="desc-content-text">
      通过特惠商城订购的商品可享受免基础运费的优惠（除新疆，西藏，青海，海南，甘肃，宁夏，内蒙等偏远地区及特殊商品），具体请以提交订单时页面展示为准（“港澳台地区暂不支持配送”）。
    </view>
    <view class="desc-content-tip">
      <text class="pt_dian">
        •
      </text>
      <text class="pt_text">
        特惠商城发货及配送时效
      </text>
    </view>
    <view class="desc-content-text">
      （1）如无特殊公告一般都是工作日在您付款后48小时内发货（非工作日延后），如超48小时还未发货的，您可以在页面点击“客服”进行查询、反馈。特殊商品发货时效以商品页面公布的实际情况为准。
    </view>
    <view class="desc-content-text">
      （2）中国大陆地区预计一般配送时效为物流收件后的7个工作日内（极偏远地区配送时间可能会更长一些），如遇交通管制、地震、暴雨洪涝灾害等不可抗力、节假日、促销活动等，商品配送时间可能会延长，具体送达时间以实际物流配送为准。
    </view>
    <view class="desc-content-text">
      （3）包裹中含液体类商品（化妆品、香水、油类等）、小家电类等送达时间需在一般商品的送货时间基础上稍有增加。
    </view>
    <view class="desc-content-tip">
      <text class="pt_dian">
        •
      </text>
      <text class="pt_text">
        温馨提示
      </text>
    </view>
    <view class="desc-content-text">
      （1）在生成订单时请您仔细选择您的收货地址所在地区，并且详细填写您的收货地址；填写地址时尽量填写上与收货地址所在路段相交的最近路段名称，否则有可能导致订单无法送达或配送超时。
    </view>
    <view class="desc-content-text">
      （2）如您在同一订单下订购多个商品时可能将由不同的商家安排发货配送，需要您分别进行签收。
    </view>
    <view class="desc-content-text">
      （3）特惠商城暂不提供指定时间送货服务，比如指定下午3点送到的服务要求目前无法提供，请谅解。特惠商城暂不支持指定物流，请谅解。
    </view>
    <view class="desc-content-text">
      （4）商品如未及时送达，您可以通过联系客服查询配送状态。
    </view>
  </view>
</template>

<style scoped lang="scss">
.toggle__title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 32rpx;
  &-text {
    font-size: 30rpx;
    color: #333333;
    line-height: 46rpx;
    font-weight: 400;
  }
}
.desc-content {
  padding-top: 0rpx;
  overflow: hidden;
  height: 0;
  transition: all 0.2s ease-in-out;

  &.active {
    padding-top: 16rpx;
    height: auto;
  }
  &-tip {
    .pt_dian {
      color: #5eae72;
      font-size: 36rpx;
    }
    .pt_text {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;
    }
  }
  &-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    margin-left: 30rpx;
  }
}
</style>
