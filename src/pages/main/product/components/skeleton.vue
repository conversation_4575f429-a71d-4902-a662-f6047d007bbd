<!--
 * @Author: houbaoguo
 * @Date: 2025-07-08
 * @Description: 商品详情页骨架屏组件 - 完全匹配真实布局结构
 * @LastEditTime: 2025-07-09 09:50:26
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
// 骨架屏组件，保持与真实页面相同的DOM结构和ID
</script>

<template>
  <view class="product-skeleton">
    <fx-navbar
      :left-show="false"
      left-color="#333"
      background="transparent"
    />
    <!-- 产品详情图片轮播，产品信息 -->
    <view id="goods-info" overflow-hidden>
      <!-- 轮播图骨架 - 模拟 product-images-swiper -->
      <view class="swiper-container">
        <view class="skeleton-swiper-item">
          <view class="skeleton-image" />
        </view>
        <!-- 分页指示器 -->
        <view class="page-indicator" />
      </view>

      <!-- 商品信息骨架  -->
      <view class="product-info">
        <!-- 价格区域 -->
        <view class="product-info__price">
          <view class="skeleton-sale-price-label" />
          <view class="skeleton-price-old" />
        </view>

        <!-- 标题 -->
        <view class="skeleton-title" />

        <!-- 标签 -->
        <view class="tag-box">
          <view
            v-for="i in 3"
            :key="i"
            class="skeleton-tag"
          />
        </view>
      </view>
    </view>

    <!-- 产品详情 -->
    <view id="goods-desc" overflow-hidden>
      <view class="card-section desc">
        <view class="card-section__title">
          <view class="skeleton-section-title" />
        </view>
      </view>
    </view>

    <!-- 相似优惠 -->
    <view id="goods-recommend" overflow-hidden>
      <view class="card-section similar-discount">
        <view class="card-section__title">
          <view class="skeleton-section-title" />
        </view>
        <view class="card-section__content">
          <view class="goods-card-list">
            <view
              v-for="i in 6"
              :key="i"
              class="goods-card-list__item"
            >
              <view class="skeleton-card-item">
                <view class="skeleton-card-image" />
                <view class="skeleton-card-title" />
                <view class="skeleton-card-price" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.product-skeleton {
  position: relative;
  height: 100vh;
  background: #f5f5f5;
  z-index: 1;
  overflow: hidden;
}

// 骨架屏基础样式
.skeleton-base {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 8rpx;
}

// 轮播图容器 - 完全匹配真实样式
.swiper-container {
  position: relative;
  margin-top: 24rpx;
  padding-bottom: 66rpx;

  .skeleton-swiper-item {
    margin: 0 24rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .skeleton-image {
      @extend .skeleton-base;
      width: 100%;
      height: 320rpx;
      border-radius: 16rpx;
    }
  }

  .page-indicator {
    @extend .skeleton-base;
    position: absolute;
    bottom: 0rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 84rpx;
    height: 40rpx;
    border-radius: 40rpx;
  }
}

// 商品信息 - 完全匹配真实样式
.product-info {
  margin: 0 24rpx;
  margin-top: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 24rpx;

  &__price {
    display: flex;
    align-items: flex-end;

    .skeleton-sale-price-label {
      @extend .skeleton-base;
      width: 60rpx;
      height: 26rpx;
      margin-right: 12rpx;
    }

    .skeleton-price-old {
      @extend .skeleton-base;
      width: 80rpx;
      height: 26rpx;
    }
  }

  .skeleton-title {
    @extend .skeleton-base;
    height: 42rpx;
    width: 85%;
    margin-top: 32rpx;
  }

  .tag-box {
    display: flex;
    align-items: center;
    margin-top: 18rpx;

    .skeleton-tag {
      @extend .skeleton-base;
      width: 60rpx;
      height: 32rpx;
      border-radius: 4rpx;

      &:not(:last-child) {
        margin-right: 16rpx;
      }
    }
  }
}

// 卡片区块 - 完全匹配真实样式
.card-section {
  margin: 0 24rpx;
  margin-top: 24rpx;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 24rpx;

  &__title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .skeleton-section-title {
      @extend .skeleton-base;
      width: 120rpx;
      height: 42rpx;
    }
  }

  &__content {
    padding-top: 20rpx;

    .goods-card-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      &__item {
        width: calc((100% - 32rpx) / 3);
        margin-bottom: 20rpx;
        box-sizing: border-box;

        .skeleton-card-item {
          .skeleton-card-image {
            @extend .skeleton-base;
            width: 100%;
            height: 160rpx;
            border-radius: 8rpx;
            margin-bottom: 12rpx;
          }

          .skeleton-card-title {
            @extend .skeleton-base;
            height: 32rpx;
            width: 90%;
            margin-bottom: 8rpx;
          }

          .skeleton-card-price {
            @extend .skeleton-base;
            height: 28rpx;
            width: 70%;
            margin-bottom: 12rpx;
          }

          .skeleton-card-button {
            @extend .skeleton-base;
            height: 56rpx;
            width: 100%;
            border-radius: 28rpx;
          }
        }
      }
    }
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
