<!--
 * @Author: houbaoguo
 * @Date: 2025-07-15
 * @Description: 确认收货页面 - 处理从H5跳转过来的确认收货操作
 * @LastEditTime: 2025-07-29 18:39:02
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import { confirmReceipt } from '@/api'
import { useConfirmReceiptEventLifecycle } from '@/events/confirmReceiptEvent'

interface ConfirmReceiptParams {
  businessType: string
  merchant_id?: string
  merchant_trade_no?: string
  transaction_id?: string
}

// 页面状态
const callbackResult = ref<ConfirmReceiptCallbackData | null>(null) // 存储回调结果

// 解析页面参数
function parsePageParams(options: any): ConfirmReceiptParams | null {
  try {
    const { merchant_id, merchant_trade_no, transaction_id } = options

    if (!merchant_id && !merchant_trade_no && !transaction_id) {
      throw new Error('缺少必要参数：businessType、merchant_id 或 merchant_trade_no')
    }

    return {
      businessType: 'weappOrderConfirm',
      merchant_id,
      merchant_trade_no,
      transaction_id,
    }
  }
  catch (err) {
    console.error('解析页面参数失败:', err)
    return null
  }
}

// 拉起确认收货组件
async function openConfirmReceipt(confirmParams: ConfirmReceiptParams) {
  try {
    uni.showLoading({
      title: '请稍后...',
      mask: true,
    })

    // 检查微信环境
    if (typeof wx === 'undefined' || !wx.openBusinessView) {
      throw new Error('当前环境不支持确认收货功能')
    }
    // 调用微信小程序确认收货组件
    // 组件会通过 wx.navigateBackMiniProgram 回调小程序
    // App.onShow 中处理回调参数
    wx.openBusinessView({
      businessType: confirmParams.businessType,
      extraData: {
        merchant_id: confirmParams.merchant_id,
        merchant_trade_no: confirmParams.merchant_trade_no,
        transaction_id: confirmParams.transaction_id,
      },
      success: (res) => {
        console.log('确认收货组件调用成功:', res)
        // 不在这里处理成功，等待 App.onShow 回调
      },
      fail: (err) => {
        console.error('确认收货组件调用失败:', err)
        handleGoBack()
      },
      complete: () => {
        console.log('确认收货组件调用完成')
        uni.hideLoading()
      },
    })
  }
  catch (err: any) {
    uni.hideLoading()
    handleGoBack()
    console.error('确认收货操作失败:', err)
  }
}

// 确认收货
async function confirmReceiptHandler(orderNo: string) {
  try {
    uni.showLoading({
      title: '请稍后...',
      mask: true,
    })
    const res = await confirmReceipt({ orderNo })
    console.log('确认收货 confirmReceiptHandler 结果:', res)
    uni.showToast({
      title: '确认收货成功',
      icon: 'success',
      duration: 1500,
    })
    setTimeout(() => {
      handleGoBack()
    }, 1000)
  }
  catch (err: any) {
    handleGoBack()
    console.error('确认收货操作失败:', err)
  }
  finally {
    uni.hideLoading()
  }
}

// 处理确认收货回调结果
function handleConfirmReceiptCallback(result: ConfirmReceiptCallbackData) {
  console.log('收到确认收货回调:', result)
  callbackResult.value = result

  if (result && result.status) {
    if (result.status === 'success') {
      const orderNo = result.req_extradata.transaction_id as string
      confirmReceiptHandler(orderNo)
    }
    else if (result.status === 'fail') {
      uni.showToast({
        title: '确认收货失败',
        icon: 'none',
        duration: 1500,
      })
      setTimeout(() => {
        handleGoBack()
      }, 1000)
    }
    else {
      handleGoBack()
    }
  }
}

// 返回上一页
function handleGoBack() {
  uni.navigateBack({
    delta: 1,
  })
}

// 页面加载
onLoad(async (options) => {
  try {
    // 解析参数
    const parsedParams = parsePageParams(options)

    if (!parsedParams) {
      return
    }
    openConfirmReceipt(parsedParams)
  }
  catch (err) {
    console.error('页面初始化失败:', err)
  }
})

// 使用确认收货事件生命周期钩子
useConfirmReceiptEventLifecycle((callbackData: ConfirmReceiptCallbackData) => {
  console.log('收到确认收货回调事件:', callbackData)

  // 处理回调结果
  handleConfirmReceiptCallback(callbackData)
})
</script>

<template>
  <view class="confirm-receipt-page">
    <!-- 导航栏 -->
    <fx-navbar
      title="确认收货"
      :left-show="true"
      @left-click="handleGoBack"
    />
  </view>
</template>

<style lang="scss" scoped>
.confirm-receipt-page {
  min-height: 100vh;
  background: #f5f5f5;
  .page-content {
    padding: 150rpx 32rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
  }
}
</style>
