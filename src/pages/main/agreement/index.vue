<!--
 * @Author: houbaoguo
 * @Date: 2025-02-25 14:38:37
 * @Description:
 * @LastEditTime: 2025-02-25 15:13:05
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { AgreementListRes, AgreementType } from '@/api/types'
import { useAgreementStore } from '@/store'

const agreementStore = useAgreementStore()
const agreementDetail = ref<AgreementListRes>()

// 获取协议详情
async function getAgreementDetail(type: AgreementType) {
  // 服务协议 隐私协议
  const data = await agreementStore.getAgreementDetailHandle({
    agreementType: [type],
  })
  agreementDetail.value = data
  uni.setNavigationBarTitle({
    title: agreementDetail.value?.agreementName || '',
  })
}

onLoad((options) => {
  getAgreementDetail(options?.type as AgreementType)
})
</script>

<template>
  <!-- eslint-disable-next-line vue/no-v-text-v-html-on-component -->
  <view class="agreement-container" v-html="agreementDetail?.agreementContent" />
</template>

<style scoped>
.agreement-container {
  width: 100%;
  height: 100%;
  padding: 32rpx;
  box-sizing: border-box;
  background-color: #fff;
}
</style>
