<script setup lang="ts">
</script>

<template>
  <view class="introduction-container">
    <view class="header-wrap">
      <image src="@img/introduction/header-bg.png" class="bg-wrap" />
      <view class="header-content">
        <view class="header-title">
          <image src="@img/introduction/title.png" class="title" />
        </view>
      </view>
    </view>
    <view class="content-wrap">
      <view class="section-wrap">
        <view class="title-wrap">
          <view class="title">
            我们是谁
          </view>
        </view>
        <view class="section-content">
          <view class="row-item">
            重庆中通电商通科技有限公司是数字化深耕服务平台，致力于为消费者，供应链提供便捷、安全的综合性数字化服务平台。
          </view>
        </view>
      </view>
      <view class="section-wrap" mb-80rpx>
        <view class="title-wrap">
          <view class="title">
            我们能做什么
          </view>
        </view>
        <view class="section-content">
          <view class="row-item">
            构建与运营综合性数字化服务平台：构建并持续优化集消费者服务与供应链协同于一体的综合性数字化平台，整合线上线下资源，为各环节参与者提供高效、互联互通的全链路数字化解决方案。
          </view>
          <view class="row-item">
            提供智能化供应链服务解决方案：我们运用先进的数字技术，为供应链上下游企业提供涵盖智能仓储管理、高效物流配送、精准库存优化及可视化协同管理等核心环节的数字化服务，助力企业降本增效，提升供应链韧性与敏捷性。
          </view>
          <view class="row-item">
            赋能消费者安全便捷的数字体验：*我们专注于打造安全、可靠、便捷的数字化消费环境，通过平台整合优质商品与服务资源，并利用大数据风控、智能推荐等技术手段，为消费者提供贯穿购物、物流及售后的全流程、高品质数字化服务体验。
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.introduction-container {
  background-color: #f5f8fd;
  .header-wrap {
    position: relative;
    .bg-wrap {
      width: 100%;
      height: 684rpx;
    }
    .header-content {
      position: absolute;
      left: 48rpx;
      top: 232rpx;
      .header-title {
        width: 388rpx;
        height: 78rpx;
        .title {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .content-wrap {
    margin-top: -200rpx;
    backdrop-filter: blur(5px);
    background-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0.7),
      rgba(255, 255, 255, 0.7) 256rpx,
      #f5f8fd 256rpx
    );
    border-radius: 48rpx 48rpx 0 0;
    border: 2rpx solid #ffffff;
    .section-wrap {
      padding: 0 32rpx;
      .title-wrap {
        margin-top: 48rpx;
        text-align: center;
        .title {
          position: relative;
          display: inline-block;
          padding: 0 24rpx;
          font-size: 36rpx;
          color: #333;
          line-height: 50rpx;
          font-weight: 500;
          &::before {
            position: absolute;
            top: 50%;
            left: -78rpx;
            content: '';
            width: 78rpx;
            height: 2rpx;
            background: linear-gradient(-90deg, #3d3d3d 0%, rgba(216, 216, 216, 0) 100%);
            transform: translate(0, -50%);
          }
          &::after {
            position: absolute;
            top: 50%;
            right: -78rpx;
            content: '';
            width: 78rpx;
            height: 2rpx;

            background: linear-gradient(90deg, #3d3d3d 0%, rgba(216, 216, 216, 0) 100%);
          }
        }
      }
      .section-content {
        margin-top: 28rpx;
        padding: 36rpx 20rpx;
        background-color: #fff;
        border-radius: 32rpx;
        &.reset {
          padding: 0;
          border-radius: unset;
          background-color: unset;
        }
        .row-item {
          position: relative;
          font-size: 28rpx;
          color: #666666;
          line-height: 40rpx;
          font-weight: 400;
          padding-left: 60rpx;
          &:not(:first-child) {
            margin-top: 32rpx;
          }
          &::before {
            content: '';
            width: 12rpx;
            height: 12rpx;
            border-radius: 50%;
            background-color: #666;
            position: absolute;
            left: 20rpx;
            top: 14rpx;
          }
        }
      }
    }
  }
}
</style>
