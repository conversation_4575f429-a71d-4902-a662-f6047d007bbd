<!--
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-21 14:53:19
 * @Description:
 * @LastEditTime: 2025-07-24 17:49:53
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { AgreementListParams } from '@/api/types'
import { AgreementTypes } from '@/api/types'
import { confirmReceiptEvent } from '@/events/confirmReceiptEvent'
import { useAgreementStore } from '@/store'

const agreementStore = useAgreementStore()

// 获取协议列表
async function getAgreementNameList() {
  try {
    const params: AgreementListParams = {
      agreementType: Object.values(AgreementTypes),
    }
    await agreementStore.getAgreementNameListHandle(params)
  }
  catch (error) {
    console.error('获取协议列表失败:', error)
  }
}

// 处理确认收货组件回调
function handleConfirmReceiptCallback(options: any) {
  console.log('检测到确认收货组件回调:', options)

  if (options?.referrerInfo && options?.referrerInfo?.extraData) {
    const extraData = options.referrerInfo.extraData

    // 构建回调数据
    const callbackData: ConfirmReceiptCallbackData = {
      status: extraData.status, // "success" | "fail" | "cancel"
      errormsg: extraData.errormsg, // 错误信息（当status为fail时）
      req_extradata: extraData.req_extradata, // 开发者调用组件时的请求信息
      timestamp: Date.now(), // 添加时间戳，避免重复处理
    }

    console.log('确认收货回调数据:', callbackData)

    // 使用事件管理器发布事件
    confirmReceiptEvent.publish(callbackData)

    console.log('确认收货回调事件已发布')
  }
}

// 应用启动
onLaunch((options) => {
  console.log('App Launch:', options)
  // 获取协议列表
  getAgreementNameList()
})

// 应用显示
onShow((options) => {
  console.log('App Show:', options)
  // 检查是否是从确认收货组件返回
  // options && [1037, 1038].includes(options.scene) 先不加，防止误判
  handleConfirmReceiptCallback(options)
})

// 应用隐藏
onHide(() => {
  console.log('App Hide')
})

// 应用错误处理
onError((error) => {
  console.error('App Error:', error)
})

// 注意：现在使用事件发布订阅模式，不再需要全局数据
</script>

<style lang="scss">
@use '@/styles/index.scss' as *;
@font-face {
  font-family: 'DIN-Bold';
  src: url('@/static/font/DIN-Bold.ttf');
}
@font-face {
  font-family: 'DIN-Medium';
  src: url('@/static/font/DIN-Medium.otf');
}
</style>
