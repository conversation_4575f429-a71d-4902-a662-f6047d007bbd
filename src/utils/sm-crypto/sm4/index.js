const DECRYPT = 0
const ROUND = 32
const BLOCK = 16

const Sbox = [
  0xD6,
  0x90,
  0xE9,
  0xFE,
  0xCC,
  0xE1,
  0x3D,
  0xB7,
  0x16,
  0xB6,
  0x14,
  0xC2,
  0x28,
  0xFB,
  0x2C,
  0x05,
  0x2B,
  0x67,
  0x9A,
  0x76,
  0x2A,
  0xBE,
  0x04,
  0xC3,
  0xAA,
  0x44,
  0x13,
  0x26,
  0x49,
  0x86,
  0x06,
  0x99,
  0x9C,
  0x42,
  0x50,
  0xF4,
  0x91,
  0xEF,
  0x98,
  0x7A,
  0x33,
  0x54,
  0x0B,
  0x43,
  0xED,
  0xCF,
  0xAC,
  0x62,
  0xE4,
  0xB3,
  0x1C,
  0xA9,
  0xC9,
  0x08,
  0xE8,
  0x95,
  0x80,
  0xDF,
  0x94,
  0xFA,
  0x75,
  0x8F,
  0x3F,
  0xA6,
  0x47,
  0x07,
  0xA7,
  0xFC,
  0xF3,
  0x73,
  0x17,
  0xBA,
  0x83,
  0x59,
  0x3C,
  0x19,
  0xE6,
  0x85,
  0x4F,
  0xA8,
  0x68,
  0x6B,
  0x81,
  0xB2,
  0x71,
  0x64,
  0xDA,
  0x8B,
  0xF8,
  0xEB,
  0x0F,
  0x4B,
  0x70,
  0x56,
  0x9D,
  0x35,
  0x1E,
  0x24,
  0x0E,
  0x5E,
  0x63,
  0x58,
  0xD1,
  0xA2,
  0x25,
  0x22,
  0x7C,
  0x3B,
  0x01,
  0x21,
  0x78,
  0x87,
  0xD4,
  0x00,
  0x46,
  0x57,
  0x9F,
  0xD3,
  0x27,
  0x52,
  0x4C,
  0x36,
  0x02,
  0xE7,
  0xA0,
  0xC4,
  0xC8,
  0x9E,
  0xEA,
  0xBF,
  0x8A,
  0xD2,
  0x40,
  0xC7,
  0x38,
  0xB5,
  0xA3,
  0xF7,
  0xF2,
  0xCE,
  0xF9,
  0x61,
  0x15,
  0xA1,
  0xE0,
  0xAE,
  0x5D,
  0xA4,
  0x9B,
  0x34,
  0x1A,
  0x55,
  0xAD,
  0x93,
  0x32,
  0x30,
  0xF5,
  0x8C,
  0xB1,
  0xE3,
  0x1D,
  0xF6,
  0xE2,
  0x2E,
  0x82,
  0x66,
  0xCA,
  0x60,
  0xC0,
  0x29,
  0x23,
  0xAB,
  0x0D,
  0x53,
  0x4E,
  0x6F,
  0xD5,
  0xDB,
  0x37,
  0x45,
  0xDE,
  0xFD,
  0x8E,
  0x2F,
  0x03,
  0xFF,
  0x6A,
  0x72,
  0x6D,
  0x6C,
  0x5B,
  0x51,
  0x8D,
  0x1B,
  0xAF,
  0x92,
  0xBB,
  0xDD,
  0xBC,
  0x7F,
  0x11,
  0xD9,
  0x5C,
  0x41,
  0x1F,
  0x10,
  0x5A,
  0xD8,
  0x0A,
  0xC1,
  0x31,
  0x88,
  0xA5,
  0xCD,
  0x7B,
  0xBD,
  0x2D,
  0x74,
  0xD0,
  0x12,
  0xB8,
  0xE5,
  0xB4,
  0xB0,
  0x89,
  0x69,
  0x97,
  0x4A,
  0x0C,
  0x96,
  0x77,
  0x7E,
  0x65,
  0xB9,
  0xF1,
  0x09,
  0xC5,
  0x6E,
  0xC6,
  0x84,
  0x18,
  0xF0,
  0x7D,
  0xEC,
  0x3A,
  0xDC,
  0x4D,
  0x20,
  0x79,
  0xEE,
  0x5F,
  0x3E,
  0xD7,
  0xCB,
  0x39,
  0x48,
]

const CK = [
  0x00070E15,
  0x1C232A31,
  0x383F464D,
  0x545B6269,
  0x70777E85,
  0x8C939AA1,
  0xA8AFB6BD,
  0xC4CBD2D9,
  0xE0E7EEF5,
  0xFC030A11,
  0x181F262D,
  0x343B4249,
  0x50575E65,
  0x6C737A81,
  0x888F969D,
  0xA4ABB2B9,
  0xC0C7CED5,
  0xDCE3EAF1,
  0xF8FF060D,
  0x141B2229,
  0x30373E45,
  0x4C535A61,
  0x686F767D,
  0x848B9299,
  0xA0A7AEB5,
  0xBCC3CAD1,
  0xD8DFE6ED,
  0xF4FB0209,
  0x10171E25,
  0x2C333A41,
  0x484F565D,
  0x646B7279,
]

/**
 * 16 进制串转字节数组
 */
function hexToArray(str) {
  const arr = []
  for (let i = 0, len = str.length; i < len; i += 2) {
    arr.push(Number.parseInt(str.substr(i, 2), 16))
  }
  return arr
}

/**
 * 字节数组转 16 进制串
 */
function ArrayToHex(arr) {
  return arr
    .map((item) => {
      item = item.toString(16)
      return item.length === 1 ? `0${item}` : item
    })
    .join('')
}

/**
 * utf8 串转字节数组
 */
function utf8ToArray(str) {
  const arr = []

  for (let i = 0, len = str.length; i < len; i++) {
    const point = str.codePointAt(i)

    if (point <= 0x007F) {
      // 单字节，标量值：00000000 00000000 0zzzzzzz
      arr.push(point)
    }
    else if (point <= 0x07FF) {
      // 双字节，标量值：00000000 00000yyy yyzzzzzz
      arr.push(0xC0 | (point >>> 6)) // 110yyyyy（0xc0-0xdf）
      arr.push(0x80 | (point & 0x3F)) // 10zzzzzz（0x80-0xbf）
    }
    else if (point <= 0xD7FF || (point >= 0xE000 && point <= 0xFFFF)) {
      // 三字节：标量值：00000000 xxxxyyyy yyzzzzzz
      arr.push(0xE0 | (point >>> 12)) // 1110xxxx（0xe0-0xef）
      arr.push(0x80 | ((point >>> 6) & 0x3F)) // 10yyyyyy（0x80-0xbf）
      arr.push(0x80 | (point & 0x3F)) // 10zzzzzz（0x80-0xbf）
    }
    else if (point >= 0x010000 && point <= 0x10FFFF) {
      // 四字节：标量值：000wwwxx xxxxyyyy yyzzzzzz
      i++
      arr.push(0xF0 | ((point >>> 18) & 0x1C)) // 11110www（0xf0-0xf7）
      arr.push(0x80 | ((point >>> 12) & 0x3F)) // 10xxxxxx（0x80-0xbf）
      arr.push(0x80 | ((point >>> 6) & 0x3F)) // 10yyyyyy（0x80-0xbf）
      arr.push(0x80 | (point & 0x3F)) // 10zzzzzz（0x80-0xbf）
    }
    else {
      // 五、六字节，暂时不支持
      arr.push(point)
      throw new Error('input is not supported')
    }
  }

  return arr
}

/**
 * 字节数组转 utf8 串
 */
function arrayToUtf8(arr) {
  const str = []
  for (let i = 0, len = arr.length; i < len; i++) {
    if (arr[i] >= 0xF0 && arr[i] <= 0xF7) {
      // 四字节
      str.push(
        String.fromCodePoint(
          ((arr[i] & 0x07) << 18)
          + ((arr[i + 1] & 0x3F) << 12)
          + ((arr[i + 2] & 0x3F) << 6)
          + (arr[i + 3] & 0x3F),
        ),
      )
      i += 3
    }
    else if (arr[i] >= 0xE0 && arr[i] <= 0xEF) {
      // 三字节
      str.push(
        String.fromCodePoint(
          ((arr[i] & 0x0F) << 12)
          + ((arr[i + 1] & 0x3F) << 6)
          + (arr[i + 2] & 0x3F),
        ),
      )
      i += 2
    }
    else if (arr[i] >= 0xC0 && arr[i] <= 0xDF) {
      // 双字节
      str.push(
        String.fromCodePoint(((arr[i] & 0x1F) << 6) + (arr[i + 1] & 0x3F)),
      )
      i++
    }
    else {
      // 单字节
      str.push(String.fromCodePoint(arr[i]))
    }
  }

  return str.join('')
}

/**
 * 32 比特循环左移
 */
function rotl(x, n) {
  const s = n & 31
  return (x << s) | (x >>> (32 - s))
}

/**
 * 非线性变换
 */
function byteSub(a) {
  return (
    ((Sbox[(a >>> 24) & 0xFF] & 0xFF) << 24)
    | ((Sbox[(a >>> 16) & 0xFF] & 0xFF) << 16)
    | ((Sbox[(a >>> 8) & 0xFF] & 0xFF) << 8)
    | (Sbox[a & 0xFF] & 0xFF)
  )
}

/**
 * 线性变换，加密/解密用
 */
function l1(b) {
  return b ^ rotl(b, 2) ^ rotl(b, 10) ^ rotl(b, 18) ^ rotl(b, 24)
}

/**
 * 线性变换，生成轮密钥用
 */
function l2(b) {
  return b ^ rotl(b, 13) ^ rotl(b, 23)
}

/**
 * 以一组 128 比特进行加密/解密操作
 */
function sms4Crypt(input, output, roundKey) {
  const x = Array.from({ length: 4 })

  // 字节数组转成字数组（此处 1 字 = 32 比特）
  const tmp = Array.from({ length: 4 })
  for (let i = 0; i < 4; i++) {
    tmp[0] = input[4 * i] & 0xFF
    tmp[1] = input[4 * i + 1] & 0xFF
    tmp[2] = input[4 * i + 2] & 0xFF
    tmp[3] = input[4 * i + 3] & 0xFF
    x[i] = (tmp[0] << 24) | (tmp[1] << 16) | (tmp[2] << 8) | tmp[3]
  }

  // x[i + 4] = x[i] ^ l1(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ roundKey[i]))
  for (let r = 0, mid; r < 32; r += 4) {
    mid = x[1] ^ x[2] ^ x[3] ^ roundKey[r + 0]
    x[0] ^= l1(byteSub(mid)) // x[4]

    mid = x[2] ^ x[3] ^ x[0] ^ roundKey[r + 1]
    x[1] ^= l1(byteSub(mid)) // x[5]

    mid = x[3] ^ x[0] ^ x[1] ^ roundKey[r + 2]
    x[2] ^= l1(byteSub(mid)) // x[6]

    mid = x[0] ^ x[1] ^ x[2] ^ roundKey[r + 3]
    x[3] ^= l1(byteSub(mid)) // x[7]
  }

  // 反序变换
  for (let j = 0; j < 16; j += 4) {
    output[j] = (x[3 - j / 4] >>> 24) & 0xFF
    output[j + 1] = (x[3 - j / 4] >>> 16) & 0xFF
    output[j + 2] = (x[3 - j / 4] >>> 8) & 0xFF
    output[j + 3] = x[3 - j / 4] & 0xFF
  }
}

/**
 * 密钥扩展算法
 */
function sms4KeyExt(key, roundKey, cryptFlag) {
  const x = Array.from({ length: 4 })

  // 字节数组转成字数组（此处 1 字 = 32 比特）
  const tmp = Array.from({ length: 4 })
  for (let i = 0; i < 4; i++) {
    tmp[0] = key[0 + 4 * i] & 0xFF
    tmp[1] = key[1 + 4 * i] & 0xFF
    tmp[2] = key[2 + 4 * i] & 0xFF
    tmp[3] = key[3 + 4 * i] & 0xFF
    x[i] = (tmp[0] << 24) | (tmp[1] << 16) | (tmp[2] << 8) | tmp[3]
  }

  // 与系统参数做异或
  x[0] ^= 0xA3B1BAC6
  x[1] ^= 0x56AA3350
  x[2] ^= 0x677D9197
  x[3] ^= 0xB27022DC

  // roundKey[i] = x[i + 4] = x[i] ^ l2(byteSub(x[i + 1] ^ x[i + 2] ^ x[i + 3] ^ CK[i]))
  for (let r = 0, mid; r < 32; r += 4) {
    mid = x[1] ^ x[2] ^ x[3] ^ CK[r + 0]
    roundKey[r + 0] = x[0] ^= l2(byteSub(mid)) // x[4]

    mid = x[2] ^ x[3] ^ x[0] ^ CK[r + 1]
    roundKey[r + 1] = x[1] ^= l2(byteSub(mid)) // x[5]

    mid = x[3] ^ x[0] ^ x[1] ^ CK[r + 2]
    roundKey[r + 2] = x[2] ^= l2(byteSub(mid)) // x[6]

    mid = x[0] ^ x[1] ^ x[2] ^ CK[r + 3]
    roundKey[r + 3] = x[3] ^= l2(byteSub(mid)) // x[7]
  }

  // 解密时使用反序的轮密钥
  if (cryptFlag === DECRYPT) {
    for (let r = 0, mid; r < 16; r++) {
      mid = roundKey[r]
      roundKey[r] = roundKey[31 - r]
      roundKey[31 - r] = mid
    }
  }
}

function sm4(
  inArray,
  key,
  cryptFlag,
  { padding = 'pkcs#7', mode, iv = [], output = 'string' } = {},
) {
  if (mode === 'cbc') {
    // CBC 模式，默认走 ECB 模式
    if (typeof iv === 'string') iv = hexToArray(iv)
    if (iv.length !== 128 / 8) {
      // iv 不是 128 比特
      throw new Error('iv is invalid')
    }
  }

  // 检查 key
  if (typeof key === 'string') key = hexToArray(key)
  if (key.length !== 128 / 8) {
    // key 不是 128 比特
    throw new Error('key is invalid')
  }

  // 检查输入
  if (typeof inArray === 'string') {
    if (cryptFlag !== DECRYPT) {
      // 加密，输入为 utf8 串
      inArray = utf8ToArray(inArray)
    }
    else {
      // 解密，输入为 16 进制串
      inArray = hexToArray(inArray)
    }
  }
  else {
    inArray = [...inArray]
  }

  // 新增填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7
  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag !== DECRYPT) {
    const paddingCount = BLOCK - (inArray.length % BLOCK)
    for (let i = 0; i < paddingCount; i++) inArray.push(paddingCount)
  }

  // 生成轮密钥
  const roundKey = Array.from({ length: ROUND })
  sms4KeyExt(key, roundKey, cryptFlag)

  const outArray = []
  let lastVector = iv
  let restLen = inArray.length
  let point = 0
  while (restLen >= BLOCK) {
    const input = inArray.slice(point, point + 16)
    const output = Array.from({ length: 16 })

    if (mode === 'cbc') {
      for (let i = 0; i < BLOCK; i++) {
        if (cryptFlag !== DECRYPT) {
          // 加密过程在组加密前进行异或
          input[i] ^= lastVector[i]
        }
      }
    }

    sms4Crypt(input, output, roundKey)

    for (let i = 0; i < BLOCK; i++) {
      if (mode === 'cbc') {
        if (cryptFlag === DECRYPT) {
          // 解密过程在组解密后进行异或
          output[i] ^= lastVector[i]
        }
      }

      outArray[point + i] = output[i]
    }

    if (mode === 'cbc') {
      if (cryptFlag !== DECRYPT) {
        // 使用上一次输出作为加密向量
        lastVector = output
      }
      else {
        // 使用上一次输入作为解密向量
        lastVector = input
      }
    }

    restLen -= BLOCK
    point += BLOCK
  }

  // 去除填充，sm4 是 16 个字节一个分组，所以统一走到 pkcs#7
  if ((padding === 'pkcs#5' || padding === 'pkcs#7') && cryptFlag === DECRYPT) {
    const len = outArray.length
    const paddingCount = outArray[len - 1]
    for (let i = 1; i <= paddingCount; i++) {
      if (outArray[len - i] !== paddingCount)
        throw new Error('padding is invalid')
    }
    outArray.splice(len - paddingCount, paddingCount)
  }

  // 调整输出
  if (output !== 'array') {
    if (cryptFlag !== DECRYPT) {
      // 加密，输出转 16 进制串
      return ArrayToHex(outArray)
    }
    else {
      // 解密，输出转 utf8 串
      return arrayToUtf8(outArray)
    }
  }
  else {
    return outArray
  }
}

export default {
  encrypt(inArray, key, options) {
    return sm4(inArray, key, 1, options)
  },
  decrypt(inArray, key, options) {
    return sm4(inArray, key, 0, options)
  },
}
