/*
 * @Author: houbaoguo
 * @Date: 2025-01-20 10:31:34
 * @Description:
 * @LastEditTime: 2025-02-13 09:33:19
 * @LastEditors: houbaoguo
 */
import SM2 from './sm2'
import SM4 from './sm4'

import { a, b, c, d } from './sm_crypto_key'

async function uuidv4() {
  const res: any = await uni.getRandomValues({
    length: 27,
  })
  return uni.arrayBufferToBase64(res.randomValues)
}
const cipherMode = 1 // 1 - C1C3C2，0 - C1C2C3，默认为1

function generateHex() {
  const key = [...Array.from({ length: 32 })]
    .map(() => Math.floor(Math.random() * 16).toString(16))
    .join('')
  return key
}

const prod = {
  CLIENT: {
    private: a,
  },
  SERVER: {
    public: b,
  },
}
const dev = {
  CLIENT: {
    private: c,
  },
  SERVER: {
    public: d,
  },
}
const CLIENT = import.meta.env.VITE_APP_ENV === 'production' ? prod.CLIENT : dev.CLIENT
const SERVER = import.meta.env.VITE_APP_ENV === 'production' ? prod.SERVER : dev.SERVER
export async function encryptData(params: AnyObject) {
  // 1.对参数进行加签处理
  const msg = JSON.stringify(params)
  const key = generateHex()
  const uuid = await uuidv4()

  // 1.对参数进行加签处理
  console.log('CLIENT.private', CLIENT.private)
  const sign = SM2.doSignature(`${msg}${uuid}`, CLIENT.private, {
    hash: true,
    der: true,
  })
  // 2.对内容进行sm4加密
  const content = SM4.encrypt(msg, key)
  // 3.生成enckey
  const encKey = `04${SM2.doEncrypt(key, SERVER.public, 1)}`

  return {
    sign,
    uuid,
    content,
    encKey,
  }
}
export function decryptData(params: AnyObject) {
  const { encKey, content } = params
  const _encKey = SM2.doDecrypt(encKey, CLIENT.private, cipherMode)
  const _content = SM4.decrypt(content, _encKey, {
    output: 'string',
  })

  return JSON.parse(_content)
}
