export const a
  = '00FA9808264FF8E7823FE2DBA8536F7B5D6D604C34C1180F6F56489CDA4FBBBB70'
export const b
  = '0496FE134FA55BF46A807C3BBBE15BA7F1C95DC1C7E7ED3FD8B6C6EF04B0CF9D80E673D61E910A3B00471A244C2BA870024B62FC6BB8B78185037C000F00D5B37E'
export const c
  = '00A7550988B6476A9D79F8515630E59F134398AE3990D7F7114C938173261964F2'
export const d
  = '0462477BCC433CB13E518A6FDF513EBFBE13F585CDF0B3754CCCCE25C5C047317475DC4ADEB19B5342511575CB391A126F5289549DCF679BDFDD9379A6D394E2C7'
export function _(_0x1fee5b: string) {
  let _0x373874 = ''.split('').reverse().join('')
  _0x1fee5b.split(
    '\u200C\u200B'.split('').reverse().join(''),
  )
    .reverse()
    .forEach((_0x4bdb57) => {
      _0x373874 += String.fromCharCode(Number.parseInt(_0x4bdb57, 0x99E43 ^ 0x99E41))
    })
  return _0x373874.toString()
}
