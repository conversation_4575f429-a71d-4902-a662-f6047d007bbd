/**
 * 通用事件总线工具 - 基于 uni.$on/$emit 的封装
 * 提供类型安全的事件发布订阅功能
 */

/**
 * 通用事件管理器基类
 */
export abstract class BaseEventManager<T = any> {
  private callbacks: Set<(data: T) => void> = new Set()
  protected eventName: string

  constructor(eventName: string) {
    this.eventName = eventName
  }

  /**
   * 订阅事件
   * @param callback 回调函数
   */
  subscribe(callback: (data: T) => void): void {
    // 添加到回调集合
    this.callbacks.add(callback)

    // 订阅 uni 事件
    uni.$on(this.eventName, callback)
  }

  /**
   * 取消订阅事件
   * @param callback 回调函数，如果不传则取消所有订阅
   */
  unsubscribe(callback?: (data: T) => void): void {
    if (callback) {
      // 取消特定回调
      this.callbacks.delete(callback)
      uni.$off(this.eventName, callback)
    }
    else {
      // 取消所有回调
      this.callbacks.clear()
      uni.$off(this.eventName)
    }
  }

  /**
   * 发布事件
   * @param data 事件数据
   */
  publish(data: T): void {
    // 发布 uni 事件
    uni.$emit(this.eventName, data)
  }

  /**
   * 获取当前订阅数量
   */
  getSubscriberCount(): number {
    return this.callbacks.size
  }

  /**
   * 清除所有订阅
   */
  clear(): void {
    this.unsubscribe()
  }
}

/**
 * 通用事件总线工具函数
 */
export const eventBus = {
  /**
   * 订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on<T = any>(eventName: string, callback: (data: T) => void): void {
    uni.$on(eventName, callback)
  },

  /**
   * 取消订阅事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  off(eventName: string, callback?: (...args: any[]) => void): void {
    uni.$off(eventName, callback)
  },

  /**
   * 发布事件
   * @param eventName 事件名称
   * @param data 数据
   */
  emit<T = any>(eventName: string, data?: T): void {
    uni.$emit(eventName, data)
  },

  /**
   * 订阅一次性事件
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  once<T = any>(eventName: string, callback: (data: T) => void): void {
    const onceCallback = (data: T) => {
      callback(data)
      uni.$off(eventName, onceCallback)
    }
    uni.$on(eventName, onceCallback)
  },
}

/**
 * 创建事件管理器的工厂函数
 * @param eventName 事件名称
 * @returns 事件管理器实例
 */
export function createEventManager<T = any>(eventName: string): BaseEventManager<T> {
  return new (class extends BaseEventManager<T> {
    constructor() {
      super(eventName)
    }
  })()
}

/**
 * Vue 组合函数：使用事件订阅
 * @param eventManager 事件管理器
 * @param callback 回调函数
 * @returns 取消订阅函数
 */
export function useEvent<T>(
  eventManager: BaseEventManager<T>,
  callback: (data: T) => void,
): () => void {
  // 订阅事件
  eventManager.subscribe(callback)

  // 返回取消订阅函数
  return () => {
    eventManager.unsubscribe(callback)
  }
}

/**
 * Vue 生命周期钩子：自动管理事件订阅
 * @param eventManager 事件管理器
 * @param callback 回调函数
 */
export function useEventLifecycle<T>(
  eventManager: BaseEventManager<T>,
  callback: (data: T) => void,
): void {
  // 页面挂载时订阅
  onMounted(() => {
    eventManager.subscribe(callback)
  })

  // 页面卸载时取消订阅
  onUnmounted(() => {
    eventManager.unsubscribe(callback)
  })
}
