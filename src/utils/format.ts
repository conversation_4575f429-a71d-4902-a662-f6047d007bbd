/**
 * 金额格式化工具方法
 */

/**
 * 格式化金额 - 移除不必要的小数点后的0
 * @param amount 金额字符串，如 "1.00", "2.98", "12.30"
 * @returns 格式化后的字符串，如 "1", "2.98", "12.3"，无效输入返回 "--"
 */
export function formatAmount(amount: string): string {
  if (!amount || typeof amount !== 'string') {
    return '--'
  }

  // 转换为数字再转回字符串，自动去除末尾的0
  const num = Number.parseFloat(amount)

  // 如果是无效数字，返回--
  if (Number.isNaN(num)) {
    return '--'
  }

  // 转换为字符串，自动去除末尾的0
  return num.toString()
}

/**
 * 金额分段格式化结果接口
 */
export interface AmountSegments {
  /** 整数部分 */
  integer: string
  /** 小数部分（包含小数点） */
  decimal: string
  /** 是否有小数部分 */
  hasDecimal: boolean
}

/**
 * 格式化金额为分段显示 - 用于UI中不同字体大小的显示
 * @param amount 金额字符串，如 "1.00", "2.98", "12.30"
 * @returns 分段结果对象，无效输入返回 "--"
 *
 * @example
 * formatAmountSegments("12.98")
 * // 返回: { integer: "12", decimal: ".98", hasDecimal: true }
 *
 * formatAmountSegments("1.00")
 * // 返回: { integer: "1", decimal: "", hasDecimal: false }
 *
 * formatAmountSegments("2.30")
 * // 返回: { integer: "2", decimal: ".3", hasDecimal: true }
 */
export function formatAmountSegments(amount: string): AmountSegments {
  if (!amount || typeof amount !== 'string') {
    return {
      integer: '--',
      decimal: '',
      hasDecimal: false,
    }
  }

  const num = Number.parseFloat(amount)

  // 如果是无效数字，返回默认值
  if (Number.isNaN(num)) {
    return {
      integer: '--',
      decimal: '',
      hasDecimal: false,
    }
  }

  // 先格式化去除末尾的0
  const formatted = formatAmount(amount)

  // 检查是否包含小数点
  if (formatted.includes('.')) {
    const [integerPart, decimalPart] = formatted.split('.')
    return {
      integer: integerPart,
      decimal: `.${decimalPart}`,
      hasDecimal: true,
    }
  }
  else {
    return {
      integer: formatted,
      decimal: '',
      hasDecimal: false,
    }
  }
}

/**
 * 格式化金额为千分位显示
 * @param amount 金额字符串
 * @param keepDecimal 是否保留小数位，默认true
 * @returns 格式化后的字符串，如 "1,234.56"，无效输入返回 "--"
 */
export function formatAmountWithComma(amount: string, keepDecimal: boolean = true): string {
  if (!amount || typeof amount !== 'string') {
    return '--'
  }

  const num = Number.parseFloat(amount)

  if (Number.isNaN(num)) {
    return '--'
  }

  // 先格式化去除末尾的0
  const formatted = keepDecimal ? formatAmount(amount) : Math.floor(num).toString()

  // 分离整数和小数部分
  const [integerPart, decimalPart] = formatted.split('.')

  // 给整数部分添加千分位分隔符
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  // 如果有小数部分，拼接返回
  if (decimalPart !== undefined) {
    return `${formattedInteger}.${decimalPart}`
  }

  return formattedInteger
}
