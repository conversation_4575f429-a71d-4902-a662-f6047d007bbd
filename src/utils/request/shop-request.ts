/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-17 18:03:16
 * @Description:
 * @LastEditTime: 2025-07-17 17:14:44
 * @LastEditors: houbaoguo
 */
import type HttpRequest from '@/Jarvis/luch-request/index.d'
import type { HttpRequestConfig } from '@/Jarvis/luch-request/index.d'
import Request from '@/Jarvis/luch-request'
import { useTaskStore, useUserStore } from '@/store'
import { getCurrentPagePath } from '@/utils'

export const request = new Request() as unknown as HttpRequest

request.setConfig((defaultConfig: HttpRequestConfig) => {
  const baseUrl = `${import.meta.env.VITE_APP_SHOP_API_URL}/${import.meta.env.VITE_APP_SHOP_BASE_API}`
  defaultConfig.baseURL = baseUrl

  defaultConfig.validateStatus = statusCode => [200, 401].includes(statusCode)
  return defaultConfig
})

// 请求拦截器
request.interceptors.request.use(
  async (config: HttpRequestConfig) => {
    const userStore = useUserStore()
    const taskStore = useTaskStore()
    // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
    config.data = config.data || {}

    // 确保header存在
    config.header = config.header || {}
    const cpath = getCurrentPagePath()
    config.header['Authori-zation'] = userStore.access_token
    config.header.TASK_PATH = cpath

    // 排出自己定义了取消请求的url
    const customGetTaskUrl: string[] = [
      '/member/coupon/c/list',
    ]
    if (!customGetTaskUrl.includes(config.url ?? '')) {
      config.getTask = function (task, config) {
        taskStore.setRequestTask({
          cpath,
          url: config.url ?? '',
          task,
        })
      }
    }

    return config
  },
  (
    config, // 可使用async await 做异步操作
  ) => Promise.reject(config),
)

// 响应拦截器
request.interceptors.response.use(
  (response: any) => {
    const taskStore = useTaskStore()
    const { toast } = useToast()

    taskStore.removeRequestTask({
      type: 'single',
      path: response.config.header?.TASK_PATH ?? '',
      url: response.config.url ?? '',
    })

    const custom = response.config?.custom
    const data = response.data
    if (data.code !== 200) {
      if (!custom || !Object.keys(custom).length) {
        // 无自定义默认对报错进行toast弹出提示
        toast(data?.message || '系统繁忙,请稍后重试')
      }
      return Promise.reject(data)
    }
    return data
  },
  (response) => {
    const { errMsg } = response
    const abortMsg = ['onUnhandledRejection:ok', 'request:fail abort', 'request:fail request:fail abort']
    if (!abortMsg.includes(errMsg) && !errMsg.includes('abort')) {
      const { toast } = useToast()
      /*  对响应错误做点什么 （statusCode !== 200） */
      toast('系统繁忙,请稍后重试')
    }
    return Promise.reject(response)
  },
)

export default request.request
