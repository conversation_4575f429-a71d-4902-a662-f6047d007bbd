/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-17 18:03:16
 * @Description:
 * @LastEditTime: 2025-01-21 13:32:19
 * @LastEditors: houbaoguo
 */
/**
 * 请求拦截
 * @param {object} http
 */
import type { HttpRequestConfig } from '@/Jarvis/luch-request/index.d'
import type HttpRequest from '@/Jarvis/luch-request/index.d'
import { getAddedRequestInterceptorsParams } from './initInterceptors.js'

export default (request: HttpRequest) => {
  request.interceptors.request.use(
    async (config: HttpRequestConfig) => {
      // 可使用async await 做异步操作
      // 初始化请求拦截器时，会执行此方法，此时data为undefined，赋予默认{}
      config.data = config.data || {}

      return await getAddedRequestInterceptorsParams(
        config as HttpRequestConfig & { header: AnyObject },
      )
    },
    (
      config, // 可使用async await 做异步操作
    ) => Promise.reject(config),
  )
}
