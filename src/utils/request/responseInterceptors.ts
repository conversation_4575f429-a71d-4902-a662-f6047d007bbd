/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-17 18:03:16
 * @Description:
 * @LastEditTime: 2025-07-23 13:23:04
 * @LastEditors: houbaoguo
 */
import type HttpRequest from '@/Jarvis/luch-request/index.d'
import type { HttpResponse } from '@/Jarvis/luch-request/index.d'
import clone from '@/Jarvis/luch-request/utils/clone'
import { useSmCryptoStore, useTaskStore } from '@/store'

/**
 * 响应拦截
 * @param {object} http
 */

export default (request: HttpRequest) => {
  request.interceptors.response.use(
    async (response: HttpResponse) => {
      const taskStore = useTaskStore()
      const smCryptoStore = useSmCryptoStore()
      // const userStore = useUserStore()
      // const { confirm } = useConfirm()
      const { toast } = useToast()
      // console.log('response', response)
      /* 对响应成功做点什么 可使用async await 做异步操作 */
      taskStore.removeRequestTask({
        type: 'single',
        path: response.config.header?.TASK_PATH ?? '',
        url: response.config.url ?? '',
      })
      let data = clone(response.data)
      if (response?.config?.header?.crypto === 'Y' && data) {
        data = await smCryptoStore.handleDecryptData({
          path: response.config.header?.TASK_PATH ?? '',
          url: response.config.url ?? '',
          params: data,
        })
      }
      // 1.登录态过期 跳转登录
      // 自定义参数
      const custom = response.config?.custom
      /* if (response.statusCode === 401) {
        // 单点登录被挤掉 00000006
        uni.hideLoading()
        return confirm({
          showCancel: false,
          content: data?.msg,
          confirmText: '我知道了',
          maskCloseable: false,
        })
          .then(() => {
            // 清除缓存，跳登录页
            userStore.cleanLoginInfo()
          })
      } */

      if (data.code !== 0) {
        if (!custom || !Object.keys(custom).length) {
          // 无自定义默认对报错进行toast弹出提示
          toast(data?.msg || '系统繁忙,请稍后重试')
        }
        return Promise.reject(data)
      }
      return data
    },
    (response) => {
      const { errMsg } = response
      const abortMsg = ['onUnhandledRejection:ok', 'request:fail abort', 'request:fail request:fail abort']
      if (!abortMsg.includes(errMsg) && !errMsg.includes('abort')) {
        // uni.$zt.toast('系统繁忙,请稍后重试')
        const { toast } = useToast()

        toast('系统繁忙,请稍后重试')
      }
      return Promise.reject(response)
    },
  )
}
