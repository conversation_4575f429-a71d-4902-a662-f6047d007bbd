/*
 * @Author: houbaoguo
 * @Date: 2025-01-17 18:03:16
 * @Description:
 * @LastEditTime: 2025-07-11 10:58:08
 * @LastEditors: houbaoguo
 */
/**
 * @des 获取接口拦截器全局参数
 */

import type { HttpRequestConfig } from '@/Jarvis/luch-request/index.d'
import { useSmCryptoStore, useTaskStore, useUserStore } from '@/store'
import { getCurrentPagePath } from '@/utils/basic'

export async function getAddedRequestInterceptorsParams(config: HttpRequestConfig & { header: AnyObject }, scene = 'request') {
  const userStore = useUserStore()
  const smCryptoStore = useSmCryptoStore()
  const taskStore = useTaskStore()

  // 排除已经设置Authorization的请求
  if (userStore.access_token && !config.header.Authorization) {
    if (scene === 'request') {
      config.header.Authorization = `Bearer ${userStore.access_token}`
    }
  }
  const cpath = getCurrentPagePath()
  config.header.TASK_PATH = cpath
  const systemInfo = uni.getSystemInfoSync()
  ;[
    'appVersion',
    'appVersionCode',
    'deviceModel',
    'deviceType',
    'deviceBrand',
    'osName',
    'osVersion',
  ].reduce(
    (p, c) => {
      p[`${c}`] = systemInfo[c as keyof UniApp.GetSystemInfoResult]
      return p
    },
    config.header,
  )
  config.header.deviceNo = systemInfo.deviceId
  config.header.platform = 'E-ECMMERCE_WECHAT_MINI'
  // config.header.deviceNo = 'E1512140B83348559240D02223C8ADD7'

  if (config.data?._custom === 1) {
    delete config.data._custom
  }
  // const st_data = config?.data
  const whiteList = [
    '/admin/file/life/upload',
    '/admin/file/life/uploadBase64',
    '/basic/getCryptoUrl',
  ]
  if (process.env.NODE_ENV !== 'development') {
    if (!whiteList.includes(config.url ?? '') && config?.data) {
      config.header.crypto = 'Y' // 是否开启报文加密
      console.log('config?.data', config?.data)
      config.data = await smCryptoStore.handleEncryptData({
        url: config.url ?? '',
        params: config.data,
      })
      console.log('config?.data', config?.data)

      // console.log("对接口参数加密的耗时:", Date.now() - st)
      // console.log("接口:", config?.url)
      // console.log("具体参数明细:", st_data)
      // console.log("---------------------------")
    }
  }
  // 排出自己定义了取消请求的url
  const customGetTaskUrl: string[] = [
    '/member/coupon/c/list',
  ]
  if (!customGetTaskUrl.includes(config.url ?? '')) {
    config.getTask = function (task, config) {
      taskStore.setRequestTask({
        cpath,
        url: config.url ?? '',
        task,
      })
    }
  }

  return config
}
