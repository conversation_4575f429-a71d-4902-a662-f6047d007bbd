/*
 * @Author: houbaoguo
 * @Date: 2025-01-17 18:03:16
 * @Description:
 * @LastEditTime: 2025-07-04 14:43:59
 * @LastEditors: houbaoguo
 */
import type HttpRequest from '@/Jarvis/luch-request/index.d'
import type { HttpRequestConfig } from '@/Jarvis/luch-request/index.d'
import Request from '@/Jarvis/luch-request'
import requestInterceptors from './requestInterceptors'
import responseInterceptors from './responseInterceptors'

export const request = new Request() as unknown as HttpRequest

request.setConfig((defaultConfig: HttpRequestConfig) => {
  defaultConfig.baseURL = import.meta.env.VITE_APP_API_URL

  defaultConfig.validateStatus = statusCode => [200, 401].includes(statusCode)
  return defaultConfig
})

requestInterceptors(request)
responseInterceptors(request)

export default request.request
