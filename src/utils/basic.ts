/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-20 10:22:20
 * @Description:
 * @LastEditTime: 2025-06-17 09:34:23
 * @LastEditors: houbaoguo
 */
/**
 * @des 获取当前路由栈实例数组
 */
export function getCurrentPageList() {
  const pages = getCurrentPages()
  return pages
}

/**
 * @des 获取当前页面路径
 */
export function getCurrentPagePath() {
  const pages = getCurrentPageList()
  // 某些特殊情况下(比如页面进行redirectTo时的一些时机)，pages可能为空数组
  return `/${pages[pages.length - 1]?.route ?? ''}`
}

// showToast，小程序环境uni.hideLoading 会把showToast也关闭掉了
export function showToast(
  title: string,
  icon: 'success' | 'error' | 'fail' | 'exception' | 'loading' | 'none' = 'none',
) {
  setTimeout(() => {
    uni.showToast({
      title,
      icon,
    })
  }, 100)
}

/**
 * @description 防抖函数（debounce）：在 delay 毫秒内只执行最后一次调用，optional 支持 immediate 首次立即执行
 * @param fn 需要防抖的函数
 * @param delay 延迟执行时间（ms）
 * @param immediate 是否首次立即执行（默认 false）
 */
export function debounce<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => TReturn,
  delay: number,
  immediate: boolean = false,
): (...args: TArgs) => void {
  let timer: ReturnType<typeof setTimeout> | null = null
  let hasExecuted = false

  return function (...args: TArgs) {
    if (timer) clearTimeout(timer)

    if (immediate && !hasExecuted) {
      console.log('立即执行')
      fn(...args)
      hasExecuted = true
    }
    else {
      timer = setTimeout(() => {
        console.log('延迟执行')
        fn(...args)
      }, delay)
    }
  }
}

/**
 * @description 节流函数（throttle）：在 delay 时间内只允许执行一次 fn，支持 leading/trailing 控制
 * @param fn 需要节流的函数
 * @param delay 间隔时间（ms）
 * @param options 配置项（是否立即执行 leading，是否结束后补执行 trailing）
 */
export function throttle<TArgs extends any[], TReturn>(
  fn: (...args: TArgs) => TReturn,
  delay: number,
  options: { leading?: boolean, trailing?: boolean } = {},
): (...args: TArgs) => void {
  let lastCall = 0
  let timer: ReturnType<typeof setTimeout> | null = null
  const { leading = true, trailing = false } = options

  return function (...args: TArgs) {
    const now = Date.now()

    const invoke = () => {
      lastCall = now
      fn(...args)
    }

    if (leading && lastCall === 0) {
      invoke()
    }

    if (now - lastCall >= delay) {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      invoke()
    }
    else if (trailing && !timer) {
      timer = setTimeout(() => {
        fn(...args)
        lastCall = Date.now()
        timer = null
      }, delay - (now - lastCall))
    }
  }
}
