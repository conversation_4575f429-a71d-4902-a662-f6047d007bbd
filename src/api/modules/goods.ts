/*
 * @Author: houbaoguo
 * @Date: 2025-07-04 15:48:08
 * @Description:
 * @LastEditTime: 2025-07-31 17:58:45
 * @LastEditors: houbaoguo
 */
import type { AddressListParams, AddressListRes, CreateOrderParams, CreateOrderRes, DataCarouselRes, EditAddressParams, EditAddressRes, GoodsCategoryRes, GoodsListRes, LoadPreOrderParams, LoadPreOrderRes, PayMent, PreOrderParams, PreOrderRes, ProductDetailRes, UpdatePaidParams, WxPayParams } from '@/api/types/goods'
import type { ResponseData } from '@/api/types/index'
import { request } from '@/utils/request/shop-request'

/**
 * @des 获取产品详情
 * @url
 * <AUTHOR>
export function getProductDetail(params: { id: number | string }, config?: AnyObject) {
  return request.get<ResponseData<ProductDetailRes>>(`/product/detail/${params.id}?type=normal`, config)
}

/**
 * @des 相似优惠
 * @url
 * <AUTHOR>
export function getProductList(params: { id: number | string, page: number, limit: number }, config?: AnyObject) {
  return request.get<ResponseData<any>>(`/products?cid=${params.id}&page=${params.page}&limit=${params.limit}`, config)
}

/**
 * @des 查询商品详情轮播图数据
 * @url
 * <AUTHOR>
export function getDataCarousel(params?: AnyObject, config?: AnyObject) {
  return request.post<ResponseData<DataCarouselRes>>(`/carouse/queryCarousel`, params, config)
}

/**
 * @des 收货地址列表
 * @url
 * <AUTHOR>
export function getAddressList(params: AddressListParams, config?: AnyObject) {
  return request.get<ResponseData<AddressListRes>>(`/address/list?source=${params.source}&page=${params.page}&limit=${params.limit}`, config)
}

/**
 * @des 编辑收货地址
 * @url
 * <AUTHOR>
export function editAddress(params: EditAddressParams, config?: AnyObject) {
  return request.post<ResponseData<EditAddressRes>>(`/address/edit`, params, config)
}

/**
 * @des 预下单
 * @url
 * <AUTHOR>
export function preOrder(params?: PreOrderParams, config?: AnyObject) {
  return request.post<ResponseData<PreOrderRes>>(`/order/pre/order`, params, config)
}

/**
 * @des 加载预下单
 * @url
 * <AUTHOR>
export function loadPreOrder(params: LoadPreOrderParams, config?: AnyObject) {
  return request.get<ResponseData<LoadPreOrderRes>>(`/order/load/pre/${params.preOrderNo}`, config)
}

/**
 * @des 创建订单
 * @url
 * <AUTHOR>
export function createOrder(params: CreateOrderParams, config?: AnyObject) {
  return request.post<ResponseData<CreateOrderRes>>(`/order/create`, params, config)
}

/**
 * @des 微信订单支付
 * @url
 * <AUTHOR>
export function wxPay(params: WxPayParams, config?: AnyObject) {
  return request.post<ResponseData<PayMent>>(`/pay/wechat/payment`, params, config)
}

/**
 * @des 热门商品推荐
 * @url
 * <AUTHOR>
export function getHotGoodsList(params: { page: number, limit: number }, config?: AnyObject) {
  return request.get<ResponseData<GoodsListRes>>(`/product/hot?page=${params.page}&limit=${params.limit}`, config)
}

/**
 * @des 更新微信支付状态
 * @url
 * <AUTHOR>
export function updatePaid(params: Omit<UpdatePaidParams, 'payStatus'> & Partial<Pick<UpdatePaidParams, 'payStatus'>>, config?: AnyObject) {
  return request.post<ResponseData<any>>(`/pay/wechat/update/paid`, params, config)
}

/**
 * @des 查询订单状态
 * @url
 * <AUTHOR>
export function getOrderStatus(params: { orderNo: string }, config?: AnyObject) {
  return request.get<ResponseData<{ payStatus: number }>>(`/order/detail/${params.orderNo}`, config)
}

/**
 * @des 确认收货
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/61613
 * <AUTHOR>
export function confirmReceipt(params: { orderNo: string }, config?: AnyObject) {
  return request.post<ResponseData<any>>(`/order/take`, params, config)
}

/**
 * @des 获取推荐列表
 * @url
 * <AUTHOR>
export function getRecommendList(params: { id: number | string, page: number, limit: number, type: string | number }, config?: AnyObject) {
  return request.get<ResponseData<GoodsListRes>>(`/index/product/${params.id}?page=${params.page}&limit=${params.limit}&type=${params.type}`, config)
}

/**
 * @des 获取商品分类
 * @url
 * <AUTHOR>
export function getGoodsCategory(config?: AnyObject) {
  return request.get<ResponseData<GoodsCategoryRes[]>>(`/category`, config)
}
