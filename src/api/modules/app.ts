/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-01-20 10:48:22
 * @Description:
 * @LastEditTime: 2025-07-03 13:05:17
 * @LastEditors: houbaoguo
 */
import type { AgreementListParams, AgreementListRes, LoginParams, LoginRes, ResponseData, SaveAgreeRecodeWithOutAuthParams, UploadImageRes } from '@/api/types/index'
import type { HttpRequestConfig } from '@/Jarvis/luch-request/index.d'
import { request } from '@/utils/request'

/**
 * @des 获取需要加解密的 api 列表
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/29094
 * <AUTHOR>
 */
export function getCryptoUrl(params: AnyObject = {}, config?: AnyObject) {
  return request.post('/basic/getCryptoUrl', params, config)
}

/**
 * @des 登录
 * @url https://yapi.oa.ztopays.com/project/25/interface/api/8271
 * <AUTHOR>
 */
export function login(params: LoginParams, config?: AnyObject) {
  return request.post<ResponseData<LoginRes>>(
    '/auth/oauth/ztolife/appLogin',
    params,
    config,
  )
}

/**
 * @des 图片上传
 * @url https://yapi.oa.ztopays.com/project/25/interface/api/8446
 * <AUTHOR>
 */
export function upload(params: HttpRequestConfig<UniApp.UploadTask>) {
  return request.upload<ResponseData<UploadImageRes>>('/admin/file/life/upload', params)
}

/**
 * @des 查询协议名称
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/38814
 * <AUTHOR>
 */
export function getAgreeListByScene(params: AgreementListParams, config: AnyObject = {}) {
  const processedParams = {
    ...params,
    agreementType: params.agreementType.join(','),
  }
  return request.post<ResponseData<AgreementListRes>>('/third/statistics/agreement/statisticsAgreementNameList', processedParams, config)
}

/**
 * @des 查询协议内容
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/38814
 * <AUTHOR>
 */
export function getAgreeDetailByScene(params: AgreementListParams, config: AnyObject = {}) {
  const processedParams = {
    ...params,
    agreementType: params.agreementType.join(','),
  }
  return request.post<ResponseData<AgreementListRes>>('/third/statistics/agreement/statisticsAgreementInfo', processedParams, config)
}

/**
 * @des 未登陆-协议阅读记录落库
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/38800
 * <AUTHOR>
 */
export function saveAgreeRecodeWithOutAuth(params: SaveAgreeRecodeWithOutAuthParams, config: AnyObject = {}) {
  return request.post('/third/statistics/agreement/saveFaceAgreementRecodeWithOutAuth', params, config)
}

/**
 * @des 协议阅读记录落库
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/38793
 * <AUTHOR>
 */
export function saveAgreeRecodeWithAuth(params: SaveAgreeRecodeWithOutAuthParams, config: AnyObject = {}) {
  return request.post('/third/statistics/agreement/saveFaceAgreementRecode', params, config)
}
