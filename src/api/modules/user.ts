/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-04-27 14:56:58
 * @Description:
 * @LastEditTime: 2025-07-07 16:34:33
 * @LastEditors: houbaoguo
 */
import type { LoginParams, LoginRes, ResponseData, ShopLoginParams, ShopLoginRes, WechatChannelInfoParams, WechatChannelInfoRes } from '@/api/types'
import { request } from '@/utils/request'

/**
 * @des 小程序登录
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/58043
 * <AUTHOR>
 */
export function unifyLogin(params: LoginParams, config?: AnyObject) {
  return request.post<ResponseData<LoginRes>>('/auth/oauth/mini/login', params, config)
}

/**
 * @des 微信小程序渠道信息查询
 * @url https://yapi.oa.ztoec.com/project/31/interface/api/58743
 * <AUTHOR>
 */
export function getWechatChannelInfo(params?: WechatChannelInfoParams, config?: AnyObject) {
  return request.post<ResponseData<WechatChannelInfoRes>>('member/user/channel/wechat_mini/query', params, config)
}

/**
 * @des 通过生活token换商城token
 * @url
 * <AUTHOR>
 */
export function loginShopByLife(params: ShopLoginParams, config?: AnyObject) {
  return request.post<ResponseData<ShopLoginRes>>('third/offer/mall/login', params, config)
}
