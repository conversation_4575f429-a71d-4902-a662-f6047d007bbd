/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-07-04 15:52:08
 * @Description:
 * @LastEditTime: 2025-07-31 17:56:40
 * @LastEditors: houbaoguo
 */

// 产品详情
export interface ProductDetailRes {
  cateIds: number[]
  commissionAmount: string
  productAttr: any[]
  productInfo: Record<string, any>
  productValue: Record<string, any>
}

// 查询商品详情轮播图数据-响应
export interface DataCarouselRes {
  contents: string[]
}

// orderType

export const OrderType = {
  // 购物车下单
  CART: 'shoppingCart',
  // 立即购买
  BUY_NOW: 'buyNow',
  // 再次购买
  AGAIN: 'again',
  // 视频号商品下单
  VIDEO: 'video',
} as const

// 预下单-参数
export interface PreOrderParams {
  preOrderType: typeof OrderType[keyof typeof OrderType]
  orderDetails: OrderDetail[]
  source: 'weixin'
  addressId?: number | string
}

export interface OrderDetail {
  productId: number | string
  attrValueId: number | string
  productNum: number | string
}

// 预下单-响应
export interface PreOrderRes {
  preOrderNo: string
}

// 加载预下单-参数
export interface LoadPreOrderParams {
  preOrderNo: string
}

// 加载预下单-响应
export interface LoadPreOrderRes {
  orderInfoVo: Record<string, any>
}

// 创建订单-参数
// {"realName": "", "phone": "", "addressId": 69, "couponId": 0, "payType": "ztopay", "useIntegral": false, "preOrderNo": "92175170810373cd8745b8b74fcb83a5a8c1de0121f0", "mark": "", "storeId": 0, "shippingType": 1}
export interface CreateOrderParams {
  realName: string
  shippingType: 1 | 2 // 1: 快递 2: 自提
  payChannel: 'routine'
  phone: string
  addressId: number
  couponId: number
  payType: string
  useIntegral: boolean
  preOrderNo: string
  mark: string
  promoterId: string
}

// 创建订单-响应
export interface CreateOrderRes {
  orderNo: string
}

// 收货地址列表-入参
export interface AddressListParams {
  source: 'wechat'
  page: number
  limit: number
}

// 收货地址列表-响应
export interface AddressListRes {
  list: {
    id: number
    realName: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }[]
}

// 编辑收货地址-参数
// {realName: "测试", phone: "18071409973", detail: "1号", id: 0, address: {province: "北京市", city: "北京市", district: "东城区", cityId: 2}, isDefault: false}'
export interface EditAddressParams {
  source: 'wechat'
  realName: string
  phone: string
  detail: string
  id: number
  address: {
    province: string
    city: string
    district: string
    cityId?: number
  }
  isDefault: boolean
}

// 编辑收货地址-响应
export interface EditAddressRes {
  id: number
}

// 微信支付-参数
export interface WxPayParams {
  orderNo: string
  payType: 'weixin'
  payChannel: 'routine'
  openId: string
}

// 微信支付-响应
export interface PayMent {
  payInfo: string
}
export interface WxPayRes {
  timeStamp: string
  nonceStr: string
  package: string
  signType: string
  paySign: string
}

// 热门商品推荐-响应

interface Pagination {
  page: number
  limit: number
  totalPage: number
  total: number
}

export interface GoodsListRes extends Pagination {
  list: GoodsListResItem[]
}

// 热门商品推荐-响应-item
export interface GoodsListResItem {
  id: number
  image: string
  storeName: string
  price: string
  otPrice: string
  commissionAmount: string
  cateIds: string[]
}

// 更新微信支付状态-参数
export interface UpdatePaidParams {
  orderNo: string
  payStatus: 2
}

// 商品分类-响应
export interface GoodsCategoryRes {
  id: number
  pid: number
  path: string
  name: string
  type: string
  url: string
  extra: string
  sort: number
  status: number
  child: GoodsCategoryRes[]
}
