/*
 * @Author: houbaoguo
 * @Date: 2025-01-21 10:34:51
 * @Description:
 * @LastEditTime: 2025-07-31 17:34:55
 * @LastEditors: houbaoguo
 */
export interface ResponseData<T> {
  code: number
  bizCode: number | string | null
  data: T
  msg: string
  traceId: string
}

export const LoginTypes = {
  WECHAT: 'WECHAT',
  WECHAT_SILENT: 'WECHAT_SILENT',
} as const

export type LoginType = typeof LoginTypes[keyof typeof LoginTypes]
export interface LoginParams {
  mobile?: string
  code?: string
  loginType: LoginType
  wechatCode?: string // 微信授权code
  wechatOpenId?: string // 微信授权openId
  source: 'dst_wechat_mini'
  marketingGroupNo?: string
}

export interface LoginRes {
  access_token: string
  user_info: {
    username: string
  }
}

// 协议类型枚举
export const AgreementTypes = {
  /** 服务协议 */
  FLUSA: 'FENXIANG_LIFE_USER_SERVICE_AGREEMENT',
  /** 隐私协议 */
  FLPP: 'FENXIANG_LIFE_PRIVACY_POLICY',
  /** 用户快捷支付协议 */
  // HCBC: 'H5_CASHIER_BING_CARD',
  /** 免输卡号协议 */
  // HCOKBC: 'H5_CASHIER_ONE_KEY_BING_CARD',
  /** 生物支付协议 */
  // BPSA: 'BIO_PAY_SERVICE_AGREEMENT',
  /** 付款用户服务协议 */
  // PUSA: 'PAY_USER_SERVICE_AGREEMENT',
  /** 个人钱包服务协议 */
  PWSA: 'PERSONAL_WALLET_SERVICE_AGREEMENT',
  /** 支付隐私政策协议 */
  HADMPP: 'H5_AGREEMENT_DEDUCT_MONEY_PAY_PRIVACY',
  /** 汇款充值协议 */
  // ZPRTUSA: 'ZTO_PAY_REMIT_TOP_UP_SERVICE_AGREEMENT',
  /** 免密支付《付款授权服务协议》 */
  // FASA: 'FUND_AUTHORIZATION_SERVICE_AGREEMENT',
  /** 实人认证ready页协议 */
  // HCFAA: 'H5_CASHIER_FACE_AUTHENTICATION_AGREEMENT',
  /** 纷享生活账号注销协议 */
  // AFLCA: 'APP_FENXIANG_LIFE_CANCELLATION_AGREEMENT',
  /** 个人钱包注销确认书 */
  // APWCC: 'APP_PERSONAL_WALLET_CANCELLATION_CONFIRMATION',
} as const

// 协议类型
export type AgreementType = typeof AgreementTypes[keyof typeof AgreementTypes]

// 查询协议列表-请求参数
export interface AgreementListParams {
  agreementType: AgreementType[]
}

// 查询协议列表-响应参数
export interface AgreementListRes {
  id?: number // 主键id
  agreementType?: string // 协议场景类型
  agreementTypeName?: string // 协议场景类型名称
  agreementName?: string // 协议名称
  subject?: string // 协议主体
  subjectName?: string // 协议主体名称
  agreementDocName?: string // 协议附件名称
  agreementDocUrl?: string // 协议下载地址
  agreementContent?: string // 协议正文
  ext1?: string // 预留字段1
  ext2?: string // 预留字段2
  ext3?: string // 预留字段3
  createUser?: string // 创建人名称
  userId?: number // 创建人Id
  delFlag?: '0' | '1' // 是否删除 0否1是 默认0
}

// 保存协议记录-请求参数
export interface SaveAgreeRecodeWithOutAuthParams {
  clientMemberNo?: string // 会员号（身份证号、会员号、手机号三选一）
  mobile?: string // 手机号（身份证号、会员号、手机号三选一）
  identityCard?: string // 身份证号（身份证号、会员号、手机号三选一）
  userName?: string // 用户姓名
  bankNo?: string // 银行卡号
  orderNo?: string // 订单号
  faceAgreementNos: number[] // 协议版本号数组（必须）
  recodeType: number
}

// 上传图片-响应参数
export interface UploadImageRes {
  bucketName: string
  fileName: string
  url: string
}

// 微信小程序渠道信息查询-请求参数
export interface WechatChannelInfoParams {
  code: string
  loginSource?: string
  clientId?: string
}

// 微信小程序渠道信息查询
export interface WechatChannelInfoRes {
  wechatOpenId: string
  wechatCode: string
  isBind: boolean
}

// 通过生活token换商城token
export interface ShopLoginParams {
  token: string
}

// 通过生活token换商城token
export interface ShopLoginRes {
  mallToken: string
}
