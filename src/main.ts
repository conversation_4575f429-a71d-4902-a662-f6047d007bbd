/*
 * @Author: houbaoguo
 * @Date: 2025-02-14 15:31:36
 * @Description:
 * @LastEditTime: 2025-03-05 10:02:19
 * @LastEditors: houbaoguo
 */
import pinia, { useInstanceStore } from '@/store'
import { createSSRApp } from 'vue'
import App from './App.vue'
import 'virtual:uno.css'

export function createApp() {
  const app = createSSRApp(App)
  app.use(pinia)
  app.mixin({
    onShow() {
      const instanceStore = useInstanceStore()
      const instance = getCurrentInstance()
      if (instance) {
        instanceStore.setInstance(instance)
      }
    },
    onUnload() {
      const instanceStore = useInstanceStore()
      instanceStore.setInstance(null)
    },
  })

  return {
    app,
  }
}
