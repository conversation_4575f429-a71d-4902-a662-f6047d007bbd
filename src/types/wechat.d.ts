/**
 * 微信小程序确认收货相关类型定义
 */

declare namespace WechatMiniprogram {
  interface OpenBusinessViewOption {
    /** 业务类型，如 weappOrderConfirm */
    businessType: string
    /** 额外数据 */
    extraData?: {
      /** 商户号 */
      merchant_id?: string
      /** 商户订单号 */
      merchant_trade_no?: string
      /** 微信支付订单号 */
      transaction_id?: string
    }
    /** 接口调用成功的回调函数 */
    success?: (result: OpenBusinessViewSuccessCallbackResult) => void
    /** 接口调用失败的回调函数 */
    fail?: (result: GeneralCallbackResult) => void
    /** 接口调用结束的回调函数（调用成功、失败都会执行） */
    complete?: (result: GeneralCallbackResult) => void
  }

  interface OpenBusinessViewSuccessCallbackResult {
    /** 错误信息 */
    errMsg: string
    /** 额外的返回信息 */
    extraData?: any
  }

  interface GeneralCallbackResult {
    /** 错误信息 */
    errMsg: string
  }
}

declare const wx: {
  /**
   * 打开微信小程序业务组件
   */
  openBusinessView: (option: WechatMiniprogram.OpenBusinessViewOption) => void
} & WechatMiniprogram.Wx

/**
 * 确认收货回调数据类型
 */
interface ConfirmReceiptCallbackData {
  /** 状态：success-成功, fail-失败, cancel-取消 */
  status: 'success' | 'fail' | 'cancel'
  /** 错误信息（当status为fail时） */
  errormsg?: string
  /** 开发者调用组件时的请求信息 */
  req_extradata: {
    /** 用户交易单号 */
    transaction_id?: string
    /** 用户交易商户号 */
    merchant_id?: string
    /** 商户订单号 */
    merchant_trade_no?: string
  }
  /** 时间戳 */
  timestamp: number
}
