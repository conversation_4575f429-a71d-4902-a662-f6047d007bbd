/*
 * @Author: houbaoguo
 * @Date: 2025-01-17 17:50:52
 * @Description:
 * @LastEditTime: 2025-01-20 09:39:34
 * @LastEditors: houbaoguo
 */
'use strict'

import * as utils from '../utils'

function encode(val: string) {
  return encodeURIComponent(val)
    .replace(/%40/g, '@')
    .replace(/%3A/gi, ':')
    .replace(/%24/g, '$')
    .replace(/%2C/gi, ',')
    .replace(/%20/g, '+')
    .replace(/%5B/gi, '[')
    .replace(/%5D/gi, ']')
}

/**
 * Build a URL by appending params to the end
 *
 * @param {string} url The base of the url (e.g., http://www.google.com)
 * @param {object} [params] The params to be appended
 * @returns {string} The formatted url
 */
export default function buildURL(url: string, params: any) {
  /* eslint no-param-reassign:0 */
  if (!params) {
    return url
  }

  let serializedParams
  if (utils.isURLSearchParams(params)) {
    serializedParams = params.toString()
  }
  else {
    const parts: string[] = []

    utils.forEach(params, (val: any, key: string) => {
      if (val === null || typeof val === 'undefined') {
        return
      }

      if (utils.isArray(val)) {
        key = `${key}[]`
      }
      else {
        val = [val]
      }

      utils.forEach(val, (v: any) => {
        if (utils.isDate(v)) {
          v = v.toISOString()
        }
        else if (utils.isObject(v)) {
          v = JSON.stringify(v)
        }
        parts.push(`${encode(key)}=${encode(v)}`)
      })
    })

    serializedParams = parts.join('&')
  }

  if (serializedParams) {
    const hashmarkIndex = url.indexOf('#')
    if (hashmarkIndex !== -1) {
      url = url.slice(0, hashmarkIndex)
    }

    url += (!url.includes('?') ? '?' : '&') + serializedParams
  }

  return url
}
