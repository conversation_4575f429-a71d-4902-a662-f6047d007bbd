/**
 * @Class Request
 * @description luch-request http请求插件
 * @version 3.0.7
 * <AUTHOR>
 * @Date 2021-09-04
 * @Email <EMAIL>
 * 文档: https://www.quanzhan.co/luch-request/
 * github: https://github.com/lei-mu/luch-request
 * DCloud: http://ext.dcloud.net.cn/plugin?id=392
 * HBuilderX: beat-3.0.4 alpha-3.0.4
 */

import type { HttpRequestConfig } from '../index.d'
import type { IInterceptorManager } from './InterceptorManager'
import { isPlainObject } from '../utils'
import clone from '../utils/clone'
import defaults from './defaults'
import dispatchRequest from './dispatchRequest'
import InterceptorManager from './InterceptorManager'
import mergeConfig from './mergeConfig'

export default class Request {
  config: HttpRequestConfig
  interceptors: {
    request: IInterceptorManager
    response: IInterceptorManager
  }

  /**
   * @param {object} arg - 全局配置
   * @param {string} arg.baseURL - 全局根路径
   * @param {object} arg.header - 全局header
   * @param {string} arg.method = [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE] - 全局默认请求方式
   * @param {string} arg.dataType = [json] - 全局默认的dataType
   * @param {string} arg.responseType = [text|arraybuffer] - 全局默认的responseType。支付宝小程序不支持
   * @param {object} arg.custom - 全局默认的自定义参数
   * @param {number} arg.timeout - 全局默认的超时时间，单位 ms。默认60000。H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)、微信小程序（2.10.0）、支付宝小程序
   * @param {boolean} arg.sslVerify - 全局默认的是否验证 ssl 证书。默认true.仅App安卓端支持（HBuilderX 2.3.3+）
   * @param {boolean} arg.withCredentials - 全局默认的跨域请求时是否携带凭证（cookies）。默认false。仅H5支持（HBuilderX 2.6.15+）
   * @param {boolean} arg.firstIpv4 - 全DNS解析时优先使用ipv4。默认false。仅 App-Android 支持 (HBuilderX 2.8.0+)
   * @param {Function(statusCode):Boolean} arg.validateStatus - 全局默认的自定义验证器。默认statusCode >= 200 && statusCode < 300
   */
  constructor(arg = {}) {
    if (!isPlainObject(arg)) {
      arg = {}
      console.warn('设置全局参数必须接收一个Object')
    }
    this.config = clone({ ...defaults, ...arg })
    this.interceptors = {
      request: new InterceptorManager(),
      response: new InterceptorManager(),
    }
  }

  /**
   * @Function
   * @param {Request~setConfigCallback} f - 设置全局默认配置
   */
  setConfig(f: (config: HttpRequestConfig) => HttpRequestConfig) {
    this.config = f(this.config)
  }

  middleware(config: HttpRequestConfig) {
    config = mergeConfig(this.config, config)
    const chain = [dispatchRequest, undefined]
    let promise: Promise<any> = Promise.resolve(config)

    this.interceptors.request.forEach(
      (interceptor: {
        fulfilled: (config: HttpRequestConfig) => Promise<any>
        rejected: (error: any) => Promise<any>
      }) => {
        chain.unshift(interceptor.fulfilled, interceptor.rejected)
      },
    )

    this.interceptors.response.forEach(
      (interceptor: {
        fulfilled: (response: any) => Promise<any>
        rejected: (error: any) => Promise<any>
      }) => {
        chain.push(interceptor.fulfilled, interceptor.rejected)
      },
    )

    while (chain.length) {
      promise = promise.then(chain.shift(), chain.shift())
    }

    return promise
  }

  /**
   * @Function
   * @param {object} config - 请求配置项
   * @prop {string} options.url - 请求路径
   * @prop {object} options.data - 请求参数
   * @prop {object} [options.responseType = config.responseType] [text|arraybuffer] - 响应的数据类型
   * @prop {object} [options.dataType = config.dataType] - 如果设为 json，会尝试对返回的数据做一次 JSON.parse
   * @prop {object} [options.header = config.header] - 请求header
   * @prop {object} [options.method = config.method] - 请求方法
   * @returns {Promise<unknown>} 返回请求的 Promise 对象
   */
  request(config = {}) {
    return this.middleware(config)
  }

  get(url: string, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      method: 'GET',
      ...options,
    })
  }

  post(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'POST',
      ...options,
    })
  }

  // #ifndef MP-ALIPAY
  put(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'PUT',
      ...options,
    })
  }

  // #endif

  // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-BAIDU
  delete(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'DELETE',
      ...options,
    })
  }

  // #endif

  // #ifdef H5 || MP-WEIXIN
  connect(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'CONNECT',
      ...options,
    })
  }

  // #endif

  // #ifdef  H5 || MP-WEIXIN || MP-BAIDU
  head(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'HEAD',
      ...options,
    })
  }

  // #endif

  // #ifdef APP-PLUS || H5 || MP-WEIXIN || MP-BAIDU
  options(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'OPTIONS',
      ...options,
    })
  }

  // #endif

  // #ifdef H5 || MP-WEIXIN
  trace(url: string, data: any, options: HttpRequestConfig = {}) {
    return this.middleware({
      url,
      data,
      method: 'TRACE',
      ...options,
    })
  }

  // #endif

  upload(url: string, config: HttpRequestConfig = {}) {
    config.url = url
    config.method = 'UPLOAD'
    return this.middleware(config)
  }

  download(url: string, config: HttpRequestConfig = {}) {
    config.url = url
    config.method = 'DOWNLOAD'
    return this.middleware(config)
  }
}

/**
 * setConfig回调
 * @return {object} - 返回操作后的config
 * @callback Request~setConfigCallback
 * @param {object} config - 全局默认config
 */
