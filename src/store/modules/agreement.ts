/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-02-25 13:07:22
 * @Description:
 * @LastEditTime: 2025-04-30 14:04:18
 * @LastEditors: houbaoguo
 */
import type { AgreementListParams, AgreementListRes, SaveAgreeRecodeWithOutAuthParams } from '@/api/types'
import { getAgreeDetailByScene, getAgreeListByScene, saveAgreeRecodeWithOutAuth } from '@/api'
import store from '@/store'

export const useAgreementStore = defineStore('agreement', () => {
  const agreementNameList = ref<AgreementListRes[]>([])
  const agreementDetail = ref<AgreementListRes>()

  // 获取协议列表
  const getAgreementNameListHandle = async (params: AgreementListParams) => {
    try {
      const res = await getAgreeListByScene(params)
      agreementNameList.value = res.data as AgreementListRes[]
    }
    catch (error) {
      console.error(error)
    }
  }

  // 获取协议详情
  const getAgreementDetailHandle = async (params: AgreementListParams) => {
    try {
      const res = await getAgreeDetailByScene(params)
      console.log(res)
      return res.data
    }
    catch (error) {
      console.error(error)
    }
  }

  // 未登录协议落库
  const setAgreementStatusWithoutLogin = async (params: SaveAgreeRecodeWithOutAuthParams) => {
    try {
      const res = await saveAgreeRecodeWithOutAuth(params)
      return res.data
    }
    catch (error) {
      console.error(error)
    }
  }

  // 登录协议落库
  const setAgreementStatusWithLogin = async (params: SaveAgreeRecodeWithOutAuthParams) => {
    try {
      const res = await saveAgreeRecodeWithOutAuth(params)
      return res.data
    }
    catch (error) {
      console.error(error)
    }
  }
  return {
    agreementNameList,
    agreementDetail,
    getAgreementNameListHandle,
    getAgreementDetailHandle,
    setAgreementStatusWithoutLogin,
    setAgreementStatusWithLogin,
  }
}, {
  persist: true,
})

export function useAgreementStoreHook() {
  return useAgreementStore(store)
}
