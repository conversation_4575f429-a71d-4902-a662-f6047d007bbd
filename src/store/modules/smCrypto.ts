/* eslint-disable style/max-statements-per-line */
import { getCryptoUrl } from '@/api'
import store from '@/store'
import { decryptData, encryptData } from '@/utils/sm-crypto'

export const useSmCryptoStore = defineStore('smCrypto', () => {
  const apiListRequest = ref<Promise<void> | null>(null)
  const apiList = ref<string[]>([])
  const encryptList = reactive<
    Record<string, Array<{ url: string, task: any }>>
  >({})
  const decryptList = reactive<
    Record<string, Array<{ url: string, task: any }>>
  >({})

  function setSmList(info: {
    key: 'encryptList' | 'decryptList'
    params: { path: string, url: string, task: any }
  }) {
    const { key, params } = info
    const listObj = key === 'encryptList' ? encryptList : decryptList
    let { path, url, task } = params

    if (path === '/') path = 'APP_BASIC'
    if (!listObj[path]) listObj[path] = []
    listObj[path].push({ url, task })
  }

  function setEncApi(urls: string[]) {
    apiList.value = urls || ['*']
  }

  function cancelTask(params: 'all' | string) {
    if (params !== 'all') {
      return
    }[encryptList, decryptList].forEach((listObj) => {
      Object.values(listObj).forEach((taskList) => {
        taskList?.forEach(item => item?.task?.cancel?.())
      })
      Object.keys(listObj).forEach(key => delete listObj[key])
    })
  }

  async function handleEncryptData(info: { url: string, params: any }) {
    const { url, params } = info
    if (url === '/basic/getCryptoUrl') return params

    const currentApiList = await getEncApiList()
    // console.log('currentApiList', currentApiList, url)
    if (!currentApiList.includes(url) && !currentApiList.includes('*')) {
      return params
    }

    if (Object.prototype.toString.call(params) !== '[object Object]') {
      throw new Error('sm_crypto/encryptData: params只能是Object')
    }
    return await encryptData(params)
  }

  async function handleDecryptData(info: {
    path: string
    url: string
    params: any
  }) {
    const { url, params } = info
    if (url === '/basic/getCryptoUrl') return params

    const currentApiList = await getEncApiList()
    if (!currentApiList.includes(url) && !currentApiList.includes('*')) {
      return params
    }

    if (Object.prototype.toString.call(params) !== '[object Object]') {
      throw new Error('sm-crypto/decryptData: params只能是Object')
    }

    return decryptData(params)
  }

  async function getEncApiList() {
    if (apiList.value.length) return apiList.value

    if (!apiListRequest.value || !apiList.value.length) {
      apiListRequest.value = new Promise<void>((resolve) => {
        getCryptoUrl()
          .then((res) => {
            const list = res.data || []
            setEncApi(list)
          })
          .finally(() => {
            resolve()
          })
      })
    }

    await apiListRequest.value
    return apiList.value
  }

  return {
    apiList,
    encryptList,
    decryptList,
    setSmList,
    setEncApi,
    cancelTask,
    handleEncryptData,
    handleDecryptData,
    getEncApiList,
  }
})

export function useSmCryptoStoreHook() {
  return useSmCryptoStore(store)
}
