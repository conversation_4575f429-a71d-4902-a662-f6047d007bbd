/*
 * @Author: houbaoguo
 * @Date: 2025-02-19 09:42:43
 * @Description:
 * @LastEditTime: 2025-02-19 10:02:27
 * @LastEditors: houbaoguo
 */
import type { ComponentInternalInstance } from 'vue'
import store from '@/store'

export const useInstanceStore = defineStore('instance', () => {
  const currentInstance = shallowRef<ComponentInternalInstance | null>(null)
  function setInstance(instance: ComponentInternalInstance | null) {
    currentInstance.value = instance
  }
  return {
    currentInstance,
    setInstance,
  }
})

export function useInstanceStoreHook() {
  return useInstanceStore(store)
}
