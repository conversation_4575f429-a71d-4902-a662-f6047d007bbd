import { getWechatChannelInfo, loginShopByLife, unifyLogin } from '@/api'
/*
 * @Author: houbaoguo
 * @Date: 2025-04-27 15:14:44
 * @Description:
 * @LastEditTime: 2025-07-08 10:33:09
 * @LastEditors: houbaoguo
 */
import { type LoginParams, LoginTypes } from '@/api/types'
import store from '@/store'

export const useUserStore = defineStore(
  'user',
  () => {
    const access_token = ref('')
    const checkIsBindWechat = ref(false) // 是否绑定微信
    const wechatOpenId = ref('')
    const mobile = ref('')
    // 获取登录code
    function getLoginCode(): Promise<string> {
      return new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: (res) => {
            res.errMsg === 'login:ok' ? resolve(res.code) : reject(res.errMsg)
          },
          fail: reject,
        })
      })
    }

    // 获取微信授权信息
    async function getWechatAuthInfo() {
      const code = await getLoginCode()
      const { data } = await getWechatChannelInfo({ code, clientId: 'DST_WECHAT_MINI' })
      checkIsBindWechat.value = data.isBind
      wechatOpenId.value = data.wechatOpenId
    }

    // 登录
    async function loginHandle(params: Omit<LoginParams, 'wechatOpenId' | 'source' | 'loginType'>) {
      const computedParams: LoginParams = {
        ...params,
        wechatOpenId: wechatOpenId.value,
        source: 'dst_wechat_mini',
        loginType: LoginTypes.WECHAT,
      }
      // 如果已绑定微信，则获取微信授权code
      if (checkIsBindWechat.value) {
        const wechatCode = await getLoginCode()
        computedParams.wechatCode = wechatCode
        computedParams.loginType = LoginTypes.WECHAT_SILENT
      }

      const { data } = await unifyLogin(computedParams)
      const token = data.access_token
      mobile.value = data.user_info.username
      const shopLoginRes = await loginShopByLife({ token }, {
        header: {
          Authorization: `Bearer ${token}`,
        },
      })
      if (!shopLoginRes?.data?.mallToken) {
        throw new Error('登录失败')
      }
      access_token.value = shopLoginRes?.data.mallToken
    }

    return {
      access_token,
      wechatOpenId,
      checkIsBindWechat,
      mobile,
      getLoginCode,
      loginHandle,
      getWechatAuthInfo,
    }
  },
)

export function useUserStoreHook() {
  return useUserStore(store)
}
