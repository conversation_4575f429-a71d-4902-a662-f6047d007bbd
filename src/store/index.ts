/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-02-14 15:31:36
 * @Description:
 * @LastEditTime: 2025-06-17 09:38:19
 * @LastEditors: houbaoguo
 */
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()
const piniaPersist = createPersistedState({
  storage: {
    getItem: uni.getStorageSync,
    setItem: uni.setStorageSync,
  },
})

pinia.use(piniaPersist)
// 导出pinia实例
export default pinia

// 导出所有store模块
export * from './modules/agreement'
export * from './modules/instance'
export * from './modules/smCrypto'
export * from './modules/task'
export * from './modules/user'
