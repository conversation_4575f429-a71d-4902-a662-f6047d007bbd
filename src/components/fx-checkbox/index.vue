<!--
 * @Author: houbaoguo
 * @Date: 2025-02-14 17:46:47
 * @Description:
 * @LastEditTime: 2025-02-14 18:10:43
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
const props = withDefaults(defineProps<{
  modelValue: boolean
  shape: 'square' | 'round'
  width: number
  height: number
}>(), {
  modelValue: false,
  shape: 'square',
  width: 24,
  height: 24,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const checked = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  },
})

/* const icon = computed(() => {
  return checked.value ? 'i-carbon:checkmark-outline' : 'i-carbon:circle-outline'
}) */

function handleClick() {
  checked.value = !checked.value
}
</script>

<template>
  <view
    class="fx-checkbox"
    :style="{
      width: `${props.width}rpx`,
      height: `${props.height}rpx`,
    }"
    @click="handleClick"
  >
    <view class="fx-checkbox__icon">
      <!-- <view :class="icon" /> -->
      <view v-if="!checked" class="unchecked i-carbon:circle-outline" />
      <view v-else class="checked i-carbon:checkmark-filled" />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.fx-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  .fx-checkbox__icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .unchecked {
      color: #CDCDCD;
    }
    .checked {
      color: #387BFF;
    }
  }
}
</style>
