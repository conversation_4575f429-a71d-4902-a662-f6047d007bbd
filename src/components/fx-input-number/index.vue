<script lang="ts" setup>
import type { BaseEvent, InputOnBlurEvent, InputOnFocusEvent, InputOnInputEvent } from '@uni-helper/uni-app-types'
import type { CSSProperties } from 'vue'
import { getMainClass, pxCheck } from '@/utils'
import { inputnumberEmits, inputnumberProps } from './inputNumber'

  type UpdateSource = '' | 'click' | 'input' | 'blur'

const props = defineProps(inputnumberProps)

const emit = defineEmits(inputnumberEmits)

const slots = useSlots()

const formDisabled = computed(() => {
  return props.disabled
})
const componentName = 'fx-input-number'

const classes = computed(() => {
  return getMainClass(props, componentName, {
    [`${componentName}--disabled`]: formDisabled.value,
  })
})

function toNumber(value: number | string) {
  if (typeof value === 'number') {
    return value
  }

  return Number(value)
}

const innerValue = computed(() => {
  return toNumber(props.modelValue)
})

const innerMinValue = computed(() => {
  return toNumber(props.min)
})

const innerMaxValue = computed(() => {
  return toNumber(props.max)
})

const innerStepValue = computed(() => {
  return toNumber(props.step)
})

const innerDigits = computed(() => {
  return toNumber(props.decimalPlaces)
})

const allowDecrease = computed(() => {
  if (formDisabled.value) {
    return false
  }

  return innerValue.value > innerMinValue.value
})

const allowIncrease = computed(() => {
  if (formDisabled.value) {
    return false
  }

  return innerValue.value < innerMaxValue.value
})

const decreaseClasses = computed(() => {
  return {
    [`${componentName}__icon--disabled`]: !allowDecrease.value,
  }
})

const inputStyles = computed(() => {
  const value: CSSProperties = {}

  const { inputWidth, buttonSize } = props

  if (inputWidth) {
    value.width = pxCheck(inputWidth)
  }
  if (buttonSize) {
    value.height = pxCheck(buttonSize)
  }

  return value
})

const buttonIconStyle = computed(() => {
  return {
    fontSize: props.buttonSize || '16px',
  }
})

const increaseClasses = computed(() => {
  return {
    [`${componentName}__icon--disabled`]: !allowIncrease.value,
  }
})

const inputValue = ref('')

let updateSource: UpdateSource = ''

function precisionValue(value: number, type: 'number'): number
function precisionValue(value: number, type: 'string'): string
function precisionValue(value: number, type: 'number' | 'string') {
  const fixedValue = value.toFixed(innerDigits.value)

  if (type === 'string') {
    return fixedValue
  }

  return Number(fixedValue)
}

function updateInputValue(value: number) {
  const finalValue = precisionValue(value, 'string')

  if (finalValue !== inputValue.value) {
    inputValue.value = finalValue
  }
  else {
    inputValue.value = ''

    nextTick(() => {
      inputValue.value = finalValue
    })
  }
}

function formatValue(value: number | string) {
  let trulyValue = Math.max(
    innerMinValue.value,
    Math.min(
      innerMaxValue.value,
      toNumber(value),
    ),
  )

  if (props.stepStrictly) {
    trulyValue = Math.round(trulyValue / innerStepValue.value) * innerStepValue.value
  }

  return precisionValue(trulyValue, 'number')
}

function emitChange(source: UpdateSource, value: number | string, event?: BaseEvent) {
  updateSource = source

  const formattedValue = formatValue(value)

  if (['', 'blur'].includes(updateSource)) {
    updateInputValue(formattedValue)
  }

  if (formattedValue !== props.modelValue) {
    emit('update:modelValue', formattedValue)
    emit('change', formattedValue, event)
  }
}

function handleInput(event: InputOnInputEvent) {
  if (formDisabled.value || props.readonly)
    return

  emitChange('input', event.detail.value, event)
}

function handleDecrease(event: BaseEvent) {
  if (formDisabled.value)
    return

  emit('reduce', event)

  const finalValue = innerValue.value - innerStepValue.value

  if (allowDecrease.value && finalValue >= innerMinValue.value) {
    emitChange('click', finalValue, event)
  }
  else {
    emit('overlimit', event, 'reduce')

    emitChange('click', innerMinValue.value, event)
  }
}

function handleIncrease(event: BaseEvent) {
  if (formDisabled.value)
    return

  emit('add', event)

  const finalValue = innerValue.value + innerStepValue.value

  if (allowIncrease.value && finalValue <= innerMaxValue.value) {
    emitChange('click', finalValue, event)
  }
  else {
    emit('overlimit', event, 'add')

    emitChange('click', innerMaxValue.value, event)
  }
}

function handleFocus(event: InputOnFocusEvent) {
  if (formDisabled.value || props.readonly)
    return

  emit('focus', event)
}

function handleBlur(event: InputOnBlurEvent) {
  if (formDisabled.value || props.readonly)
    return

  emit('blur', event)

  emitChange('blur', event.detail.value, event)
}

function correctValue() {
  emitChange('', props.modelValue)
}

watch(() => props.modelValue, () => {
  if (updateSource === 'input') {
    updateSource = ''
    return
  }

  correctValue()
})

watch(() => [
  props.min,
  props.max,
  props.step,
  props.stepStrictly,
  props.decimalPlaces,
], () => {
  correctValue()
})

onMounted(() => {
  correctValue()
})
</script>

<template>
  <view :class="classes" :style="props.customStyle">
    <view
      class="fx-input-number__icon fx-input-number__left"
      :class="decreaseClasses"
      @click="handleDecrease"
    >
      <slot v-if="slots.leftIcon" name="leftIcon" />
      <view
        v-else
        class="i-carbon:subtract"
        :style="buttonIconStyle"
      />
    </view>

    <view v-if="props.readonly" class="fx-input-number__text--readonly">
      {{ inputValue }}
    </view>

    <template v-else>
      <!-- #ifdef H5 -->
      <input
        v-model="inputValue"
        v-bind="$attrs"
        class="fx-input-number__text--input"
        :style="inputStyles"
        type="number"
        :min="props.min"
        :max="props.max"
        :disabled="formDisabled"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      >
      <!-- #endif -->

      <!-- #ifndef H5 -->
      <input
        v-model="inputValue"
        class="fx-input-number__text--input"
        :style="inputStyles"
        type="number"
        :min="props.min"
        :max="props.max"
        :disabled="formDisabled"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      >
      <!-- #endif -->
    </template>

    <view
      class="fx-input-number__icon fx-input-number__right"
      :class="increaseClasses"
      @click="handleIncrease"
    >
      <slot v-if="slots.rightIcon" name="rightIcon" />
      <view
        v-else
        class="i-carbon:add"
        :style="buttonIconStyle"
      />
    </view>
  </view>
</template>

  <style lang="scss" scoped>
.fx-input-number {
  box-sizing: $inputnumber-border-box;
  display: $inputnumber-display;
  align-items: center;
  height: $inputnumber-height;
  line-height: $inputnumber-line-height;
  border: $inputnumber-border;
  border-radius: $inputnumber-border-radius;

  &--disabled {
    input {
      color: $inputnumber-icon-void-color;
    }
  }

  &__icon {
    display: flex;
    align-items: center;
    color: $inputnumber-icon-color;
    cursor: pointer;

    .fx-icon {
      width: $inputnumber-icon-size;
      height: $inputnumber-icon-size;
      font-size: $inputnumber-icon-size;
    }

    &--disabled {
      color: $inputnumber-icon-void-color;
      cursor: not-allowed;
    }
  }

  input {
    border-top: 0 !important;
    border-bottom: 0 !important;
  }

  input,
  &__text--readonly,
  &__text--input {
    display: flex;
    align-items: center;
    justify-content: center;
    width: $inputnumber-input-width;
    height: 100%;
    margin: $inputnumber-input-margin;
    font-size: $inputnumber-input-font-size;
    color: $inputnumber-input-font-color;
    text-align: center;
    background-color: $inputnumber-input-background-color;
    border: $inputnumber-input-border;
    border-radius: $inputnumber-input-border-radius;
    outline: none;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    appearance: none;
  }
}
</style>
