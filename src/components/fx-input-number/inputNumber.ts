/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-06-23 11:52:18
 * @Description:
 * @LastEditTime: 2025-06-23 11:52:47
 * @LastEditors: houbaoguo
 */
import type { BaseEvent, InputOnBlurEvent, InputOnFocusEvent } from '@uni-helper/uni-app-types'
import type { ExtractPropTypes } from 'vue'
import { commonProps, makeNumericProp, nullableBooleanProp } from '@/utils'

export const inputnumberProps = {
  ...commonProps,
  /**
   * @description 初始值
   */
  modelValue: makeNumericProp(0),
  /**
   * @description 最小值限制
   */
  min: makeNumericProp(1),
  /**
   * @description 最大值限制
   */
  max: makeNumericProp(9999),
  /**
   * @description 步长
   */
  step: makeNumericProp(1),
  /**
   * @description 是否只能输入 step 的倍数
   */
  stepStrictly: Boolean,
  /**
   * @description 设置保留的小数位
   */
  decimalPlaces: makeNumericProp(0),
  /**
   * @description 禁用所有功能
   */
  disabled: nullableBooleanProp,
  /**
   * @description 只读状态禁用输入框操作行为
   */
  readonly: Boolean,
  /**
   * @description 输入框宽度
   */
  inputWidth: makeNumericProp(''),
  /**
   * @description 操作加减按钮的尺寸
   */
  buttonSize: makeNumericProp(''),
}

export type InputNumberProps = ExtractPropTypes<typeof inputnumberProps>

/* eslint-disable unused-imports/no-unused-vars */
export const inputnumberEmits = {
  'update:modelValue': (value: number) => true,
  'change': (value: number, event?: BaseEvent) => true,
  'focus': (event: InputOnFocusEvent) => true,
  'blur': (event: InputOnBlurEvent) => true,
  'reduce': (event: BaseEvent) => true,
  'add': (event: BaseEvent) => true,
  'overlimit': (event: BaseEvent, type: 'reduce' | 'add') => true,
}
/* eslint-enable unused-imports/no-unused-vars */

export type InputNumberEmits = typeof inputnumberEmits
