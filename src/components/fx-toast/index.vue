<!--
 * @Author: houbaoguo
 * @Date: 2025-02-13 13:20:23
 * @Description:
 * @LastEditTime: 2025-07-18 17:38:51
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { ToastOptions } from './types'

const toastOptions = ref<ToastOptions>({
  message: '',
  duration: 1500,
})
const toastVisible = ref(false)

function show(options: ToastOptions) {
  toastOptions.value = options
  toastVisible.value = true
  setTimeout(() => {
    toastVisible.value = false
  }, options.duration)
}

defineExpose({
  show,
})
</script>

<template>
  <view
    v-if="toastVisible"
    class="fx-toast"
    :class="{ top: toastOptions.position === 'top', bottom: toastOptions.position === 'bottom' }"
  >
    <view class="fx-toast-content">
      {{ toastOptions.message }}
    </view>
  </view>
</template>

<style scoped lang="scss">
.fx-toast {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.7);
  top: 35vh;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 24rpx;
  padding: 20rpx 40rpx;
  z-index: 99999999;
  &.top {
    top: 20vh;
  }
  &.bottom {
    bottom: 20vh;
  }
  .fx-toast-content {
    max-width: calc(100vw - 64rpx);
    text-align: center;
    color: #fff;
    line-height: 40rpx;
    font-size: $font-size-base;
    white-space: nowrap;
  }
}
</style>
