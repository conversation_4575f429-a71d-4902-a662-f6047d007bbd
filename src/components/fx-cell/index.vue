<script setup lang="ts">
interface Props {
  title?: string
  titleColor?: string
  subTitle?: string
  subTitleColor?: string
  content?: string
  contentColor?: string
  isLink?: boolean
  clickable?: boolean
  to?: string
  needBottomLine?: boolean
  hoverClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  titleColor: '#151D32',
  subTitle: '',
  subTitleColor: '#787C89',
  content: '',
  contentColor: '#787C89',
  isLink: false,
  clickable: false,
  to: '',
  needBottomLine: false,
  hoverClass: 'fx-cell-hover',
})

const emit = defineEmits(['click'])
const { toHref } = useRouter()
function handleClick(event: MouseEvent) {
  emit('click', event)
  if (props.to && props.isLink) {
    toHref(props.to)
  }
}
</script>

<template>
  <view
    class="fx-cell"
    :class="{
      'is-link': isLink,
      'is-clickable': isLink || clickable,
      'is-need-bottom-line': needBottomLine,
    }"
    :hover-class="hoverClass"
    @click="handleClick"
  >
    <slot name="left-icon" />
    <view class="fx-cell__title" :class="{ 'ml-16rpx': $slots['left-icon'] }">
      <slot name="title">
        <view
          v-if="title"
          class="fx-cell__title-text"
          :style="{ color: titleColor }"
        >
          {{ title }}
        </view>
      </slot>
      <slot name="sub-title">
        <view
          v-if="subTitle"
          class="fx-cell__sub-title"
          :style="{ color: subTitleColor }"
        >
          {{ subTitle }}
        </view>
      </slot>
    </view>

    <view class="fx-cell__content">
      <slot name="content">
        <text
          v-if="content"
          class="fx-cell__text"
          :style="{ color: contentColor }"
        >
          {{ content }}
        </text>
      </slot>
      <slot name="right-icon">
        <view v-if="isLink" class="fx-cell__right-icon">
          <image
            class="right-icon-image"
            src="@/static/images/common/arrow-right.png"
            mode="aspectFit"
          />
        </view>
      </slot>
    </view>
  </view>
</template>

<style scoped lang="scss">
.fx-cell {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background-color: #fff;
  font-size: $font-size-base;
  line-height: 48rpx;
  &.is-need-bottom-line {
    &::after {
      content: '';
      position: absolute;
      left: 32rpx;
      right: 32rpx;
      bottom: 0;
      height: 2rpx;
      background-color: #eee;
      transform: scaleY(0.5);
    }
  }

  &.is-clickable {
    cursor: pointer;

    &.fx-cell-hover {
      background-color: #f2f3f5;
    }
  }

  &__title {
    flex: none;
    margin-right: 24rpx;
  }

  &__title-text {
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
  }

  &__sub-title {
    margin-top: 2rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 30rpx;
  }

  &__content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-size: 24rpx;
    line-height: 34rpx;
    font-weight: 400;
  }

  &__text {
    flex: 1;
    text-align: right;
  }

  &__right-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
    .right-icon-image {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>
