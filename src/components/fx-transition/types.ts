/*
 * @Author: houbaoguo
 * @Date: 2025-02-13 17:14:08
 * @Description:
 * @LastEditTime: 2025-02-13 17:16:16
 * @LastEditors: houbaoguo
 */
import type { defaultAnimations } from './use-transition'

export type TransitionName = keyof typeof defaultAnimations

export interface FxAnimation {
  enter: string
  leave: string
}

export const fxAnimationName = ['fade', 'fade-up', 'fade-down', 'fade-left', 'fade-right', 'slide-up', 'slide-down', 'slide-left', 'slide-right', 'zoom', 'none'] as const
export type FxAnimationName = (typeof fxAnimationName)[number]
export const fxAnimationtimingFunction = ['linear', 'ease', 'ease-in', 'ease-in-out', 'ease-out', 'step-start', 'step-end'] as const
export type FxAnimationtimingFunction = (typeof fxAnimationtimingFunction)[number]
export interface FxAnimations {
  [key: string]: FxAnimation
}
