<!--
 * @Author: houbaoguo
 * @Date: 2025-02-13 17:12:34
 * @Description:
 * @LastEditTime: 2025-03-11 15:30:09
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import { transitionEmits, transitionProps } from './transition'
import { useTransition } from './use-transition'

const props = defineProps(transitionProps)
const emits = defineEmits(transitionEmits)
const { display, classes, clickHandler, styles } = useTransition(props, emits)
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <view
    v-if="!props.destroyOnClose || display"
    :class="classes"
    :style="styles"
    @click="clickHandler"
  >
    <slot />
  </view>
</template>

<style lang="scss" scoped>
@use '@/styles/animation/index.scss' as *;
.fx-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: $overlay-bg-color;
}

.fx-overflow-hidden {
  overflow: hidden !important;
}
.fx-hidden {
  display: none !important;
}
</style>
