<!--
 * @Author: houbaoguo
 * @Date: 2025-02-25 13:07:22
 * @Description:
 * @LastEditTime: 2025-08-01 10:26:58
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
// import type { CSSProperties } from 'vue'
import { TAB_KEY } from '../fx-tabs/tabs'

defineOptions({
  name: 'FxTabItem',
})

defineProps({
  title: {
    type: String,
    required: true,
  },
  name: {
    type: [String, Number],
    required: false,
  },
  disabled: Boolean,
})

useInject<{ activeIndex: ComputedRef<string>, animatedTime: ComputedRef<number> }>(TAB_KEY)
// const { parent } = useInject<{ activeIndex: ComputedRef<string>, animatedTime: ComputedRef<number> }>(TAB_KEY)
// const tabItemStyle = computed<CSSProperties>(() => {
//   return {
//     display: parent?.animatedTime.value === 0 && parent?.activeIndex.value !== props.name ? 'none' : 'block',
//   }
// })
</script>

<script lang="ts">
export default defineComponent({
  name: 'FxTabItem',
  options: {
    virtualHost: true,
  },
})
</script>

<template>
  <view class="fx-tab-item">
    <slot />
  </view>
</template>

<style lang="scss" scoped>
.fx-tab-item {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  box-sizing: border-box;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}
</style>
