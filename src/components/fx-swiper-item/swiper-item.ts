/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-06-17 18:14:51
 * @Description:
 * @LastEditTime: 2025-06-17 18:15:01
 * @LastEditors: houbaoguo
 */
import type { ExtractPropTypes } from 'vue'
import { commonProps } from '@/utils'

export const swiperItemProps = {
  ...commonProps,
}

export type SwiperItemProps = ExtractPropTypes<typeof swiperItemProps>

export interface SwiperItemInst {
  setOffset: (offset: number) => void
}
