<!--
 * @Author: houbaoguo
 * @Date: 2025-06-17 18:11:23
 * @Description:
 * @LastEditTime: 2025-07-11 15:02:34
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { ComputedRef, CSSProperties } from 'vue'
import type { SwiperProps } from '../fx-swiper/swiper'
import { useInject } from '@/composables/useInject'
import { getMainClass, getMainStyle } from '@/utils'
import { SWIPER_KEY } from '../fx-swiper/swiper'
import { swiperItemProps } from './swiper-item'

const props = defineProps(swiperItemProps)
const { parent } = useInject<{ size: ComputedRef<number>, props: Required<SwiperProps>, gap: number }>(SWIPER_KEY)

const state = reactive({
  offset: 0,
})

const classes = computed(() => {
  return getMainClass(props, 'fx-swiper-item')
})

const style = computed<string>(() => {
  const style = {} as CSSProperties
  const direction = parent?.props.direction
  if (parent?.size.value)
    style[direction === 'horizontal' ? 'width' : 'height'] = `${parent?.size.value}px`

  if (state.offset)
    style.transform = `translate${direction === 'horizontal' ? 'X' : 'Y'}(${state.offset}px)`

  return getMainStyle(props, style)
})

function setOffset(offset: number) {
  state.offset = offset
}

defineExpose({ setOffset })
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <view :class="classes" :style="style">
    <slot />
  </view>
</template>

  <style lang="scss">
.fx-swiper-item {
  height: 100%;
}
</style>
