<!--
 * @Author: houbaoguo
 * @Date: 2025-07-03 17:44:14
 * @Description:
 * @LastEditTime: 2025-07-12 11:24:09
 * @LastEditors: houbaoguo
-->
<!--
 * @Description: 金额显示组件 - 支持不同字体大小的整数和小数部分
-->
<script setup lang="ts">
import { type AmountSegments, formatAmountSegments } from '@/utils'
import { computed } from 'vue'

interface Props {
  /** 金额字符串 */
  amount: string
  /** 统一字体大小，传入时整数和小数部分使用相同大小 */
  size?: string
  /** 整数部分字体大小，默认32rpx */
  integerSize?: string
  /** 小数部分字体大小，默认24rpx */
  decimalSize?: string
  /** 字体颜色 */
  color?: string
  /** 字体粗细 */
  fontWeight?: string | number
  /** 是否显示货币符号 */
  showSymbol?: boolean
  /** 货币符号，默认¥ */
  symbol?: string
  /** 货币符号字体大小 */
  symbolSize?: string
  /** 货币符号字体粗细 */
  symbolFontWeight?: string | number
  /** 是否显示删除线 */
  showLineThrough?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  integerSize: '32rpx',
  decimalSize: '24rpx',
  color: '#333',
  fontWeight: 'normal',
  showSymbol: false,
  symbol: '¥',
})

// 计算实际使用的字体大小
const actualIntegerSize = computed(() => {
  return props.size || props.integerSize
})

const actualDecimalSize = computed(() => {
  return props.size || props.decimalSize
})

// 计算货币符号的实际样式
const actualSymbolSize = computed(() => {
  return props.symbolSize || props.size || props.integerSize
})

const actualSymbolFontWeight = computed(() => {
  return props.symbolFontWeight || props.fontWeight
})

// 格式化金额分段
const amountSegments = computed<AmountSegments>(() => {
  const result = formatAmountSegments(props.amount)
  return result
})
</script>

<template>
  <view class="amount-display" :style="{ textDecoration: showLineThrough ? 'line-through' : 'none' }">
    <!-- 货币符号 -->
    <text
      v-if="showSymbol"
      class="amount-symbol"
      :style="{
        fontSize: actualSymbolSize,
        color,
        fontWeight: actualSymbolFontWeight,
      }"
    >
      {{ symbol }}
    </text>

    <!-- 整数部分 -->
    <text
      class="amount-integer"
      :style="{
        fontSize: actualIntegerSize,
        color,
        fontWeight,
      }"
    >
      {{ amountSegments.integer || '--' }}
    </text>

    <!-- 小数部分 -->
    <text
      v-if="amountSegments.hasDecimal"
      class="amount-decimal"
      :style="{
        marginLeft: amountSegments.integer.length === 1 ? '-2rpx' : '0',
        fontSize: actualDecimalSize,
        color,
        fontWeight,
      }"
    >
      {{ amountSegments.decimal }}
    </text>
  </view>
</template>

<style scoped lang="scss">
.amount-display {
  display: inline-flex;
  align-items: baseline;
  min-height: 1em; // 确保最小高度

  .amount-symbol {
    line-height: 1.2;
    display: inline-block;
    margin-right: 4rpx;
  }

  .amount-integer {
    line-height: 1.2;
    display: inline-block;
    // min-width: 1em; // 确保最小宽度
    font-family: 'DIN-Medium';
  }

  .amount-decimal {
    line-height: 1.2;
    display: inline-block;
    margin-left: 2rpx;
    font-family: 'DIN-Medium';
  }
}
</style>
