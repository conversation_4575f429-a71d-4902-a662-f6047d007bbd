/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-02-12 11:02:26
 * @Description:
 * @LastEditTime: 2025-04-28 11:10:49
 * @LastEditors: houbaoguo
 */
export interface ConfirmOptions {
  width?: number
  // 开放能力
  openType?: string

  // 基础配置
  title?: string
  content?: string
  showCancel?: boolean
  cancelText?: string
  confirmText?: string
  isBtnMode?: boolean
  btnColMode?: boolean

  // 交互配置
  maskCloseable?: boolean
  maskOpacity?: number
  showCloseIcon?: boolean

  // 自定义样式
  cancelBtnColor?: string
  confirmBtnColor?: string
  cancelTextColor?: string
  confirmTextColor?: string
  contentStyle?: string
  zIndex?: number
}
