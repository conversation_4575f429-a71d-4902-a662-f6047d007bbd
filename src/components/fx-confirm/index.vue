<script setup lang="ts">
import type { ConfirmOptions } from './types'
import { getCurrentPagePath } from '@/utils'

const props = withDefaults(
  defineProps<
    ConfirmOptions & {
      fxConfirmVisible: boolean
      onConfirm: () => void
      onCancel: () => void
    }
  >(),
  {
    fxConfirmVisible: false,
    width: 580,
    title: '',
    content: '内容',
    showCancel: true,
    cancelText: '取消',
    confirmText: '确定',
    maskCloseable: true,
    isBtnMode: false,
    btnColMode: false,
    showCloseIcon: false,
    maskOpacity: 0.5,
    cancelTextColor: '#333',
    confirmTextColor: '#387BFF',
    cancelBtnColor: 'transparent',
    confirmBtnColor: 'transparent',
    contentStyle: '',
    zIndex: 1000,
    onConfirm: () => {},
    onCancel: () => {},
  },
)

const emit = defineEmits<{
  (e: 'update:fxConfirmVisible', value: boolean): void
}>()

const confirmOptions = ref<
  ConfirmOptions & {
    onConfirm: () => void
    onCancel: () => void
  }
>({
        onConfirm: () => {},
        onCancel: () => {},
      })

const innerVisible = computed({
  get() {
    return props.fxConfirmVisible
  },
  set(val) {
    emit('update:fxConfirmVisible', val)
  },
})
const cancelBtnStyle = computed(() => {
  if (confirmOptions.value.isBtnMode && !confirmOptions.value.btnColMode) {
    return {
      background:
        confirmOptions.value.cancelBtnColor === 'transparent'
          ? '#fff'
          : confirmOptions.value.cancelBtnColor,
      color: '#333',
      fontSize: '28rpx',
      fontWeight: 500,
      width: '242rpx',
      height: '72rpx',
      borderRadius: '999rpx',
      border: '1rpx solid #999',
    }
  }
  if (confirmOptions.value.btnColMode) {
    return {
      background: confirmOptions.value.cancelBtnColor,
      color: '#787C89',
      fontSize: '32rpx',
      fontWeight: 400,
      border: 'none',
      marginTop: '22rpx',
    }
  }

  return {
    padding: '24rpx',
    background: confirmOptions.value.cancelBtnColor,
    color: confirmOptions.value.cancelTextColor,
    fontSize: '32rpx',
    fontWeight: 400,
  }
})

const confirmBtnStyle = computed(() => {
  if (confirmOptions.value.isBtnMode && !confirmOptions.value.btnColMode) {
    return {
      background:
        confirmOptions.value.confirmBtnColor === 'transparent'
          ? '#387BFF'
          : confirmOptions.value.confirmBtnColor,
      color: '#fff',
      fontSize: '28rpx',
      fontWeight: 500,
      width: '242rpx',
      height: '72rpx',
      borderRadius: '999rpx',
      border: 'none',
    }
  }
  if (confirmOptions.value.btnColMode) {
    return {
      background:
        confirmOptions.value.confirmBtnColor === 'transparent'
          ? '#387BFF'
          : confirmOptions.value.confirmBtnColor,
      color: '#fff',
      fontSize: '32rpx',
      fontWeight: 500,
      width: '100%',
      height: '88rpx',
      borderRadius: '999rpx',
      border: 'none',
    }
  }

  return {
    padding: '24rpx',
    background: confirmOptions.value.confirmBtnColor,
    color: confirmOptions.value.confirmTextColor,
    fontSize: '32rpx',
    fontWeight: 500,
    border: 'none',
  }
})

// 确认处理
async function handleConfirm() {
  confirmOptions.value.onConfirm()
  close()
}

// 取消处理
function handleCancel() {
  confirmOptions.value.onCancel()
  close()
}

// 关闭处理
function close() {
  emit('update:fxConfirmVisible', false)
}

watch(
  () => props,
  async (val) => {
    confirmOptions.value = {
      ...confirmOptions.value,
      ...val,
    }
  },
  { immediate: true },
)

watch(
  () => props.fxConfirmVisible,
  (val) => {
    const tabbarPaths = ['/pages/main/home/<USER>', '/pages/main/user/index']
    const currentPath = getCurrentPagePath()
    if (!tabbarPaths.includes(currentPath)) return
    if (val) {
      uni.hideTabBar({
        animation: true,
      })
    }
    else {
      uni.showTabBar({
        animation: true,
      })
    }
  },
  { immediate: true },
)

defineExpose({
  show: (
    options: ConfirmOptions & {
      onConfirm: () => void
      onCancel: () => void
    },
  ) => {
    confirmOptions.value = {
      ...props,
      ...confirmOptions.value,
      ...options,
    }

    emit('update:fxConfirmVisible', true)
  },
})
</script>

<template>
  <fx-popup
    v-model:visible="innerVisible"
    :z-index="confirmOptions.zIndex"
    :mask-closeable="confirmOptions.maskCloseable"
    transition="zoom"
  >
    <view
      class="fx-confirm"
      :style="{ width: `${confirmOptions.width}rpx` }"
      :class="{
        'fx-confirm-btn-mode': confirmOptions.isBtnMode,
        'fx-confirm-btn-mode-col': confirmOptions.btnColMode,
        'fx-confirm-btn-mode-col-no-cancel': !confirmOptions.showCancel && confirmOptions.btnColMode && confirmOptions.isBtnMode,
      }"
      @tap.stop
    >
      <view v-if="confirmOptions.title" class="fx-confirm-header">
        <text class="fx-confirm-title">
          {{ confirmOptions.title }}
        </text>
      </view>
      <view
        v-if="confirmOptions.showCloseIcon"
        class="close-icon"
        i-carbon:close
        @tap="close"
      />

      <view class="fx-confirm-body">
        <slot name="content">
          <template v-if="confirmOptions.content">
            <text
              class="fx-confirm-content"
              :class="{ noTitle: !confirmOptions.title }"
              :style="confirmOptions.contentStyle"
            >
              {{ confirmOptions.content }}
            </text>
          </template>
        </slot>
      </view>

      <view
        class="fx-confirm-footer"
        :class="{ 'fx-confirm-footer-vertical': confirmOptions.btnColMode }"
      >
        <slot name="footer">
          <fx-button
            v-if="confirmOptions.showCancel"
            class="btn-cancel"
            :radius="0"
            block
            w-full
            :custom-style="cancelBtnStyle"
            @click="handleCancel"
          >
            {{ confirmOptions.cancelText }}
          </fx-button>
          <fx-button
            type="primary"
            class="btn-confirm"
            :radius="0"
            block
            w-full
            :custom-style="confirmBtnStyle"
            @click="handleConfirm"
          >
            {{ confirmOptions.confirmText }}
          </fx-button>
        </slot>
      </view>
    </view>
  </fx-popup>
</template>

<style lang="scss">
.fx-confirm {
  position: relative;
  background: #fff;
  border-radius: 24rpx;
  width: 580rpx;
  padding-top: 48rpx;
  box-sizing: border-box;

  .close-icon {
    position: absolute;
    width: 48rpx;
    height: 48rpx;
    color: #454a5b;
    top: 24rpx;
    right: 24rpx;
  }
  &-btn-mode {
    padding-bottom: 48rpx;
    .fx-confirm-footer {
      padding: 0 40rpx;
      border-top: unset;
      .btn-cancel {
        &::after {
          content: unset;
        }
      }
    }
    &-col {
      padding-bottom: 32rpx;
    }
    &-col-no-cancel {
      padding-bottom: 48rpx;
    }
  }

  &-header {
    text-align: center;
    padding: 0 50rpx 24rpx;
  }

  &-title {
    font-size: $font-size-large;
    font-weight: 500;
    color: #333;
    word-break: break-all;
  }

  &-body {
    padding: 0 50rpx 48rpx;
    text-align: center;
  }

  &-content {
    font-size: $font-size-base;
    color: #666;
    line-height: 1.5;
    &.noTitle {
      font-size: $font-size-large;
      font-weight: 500;
      color: #333;
    }
  }

  &-footer {
    display: flex;
    border-top: 1rpx solid #f2f2f2;
    font-size: $font-size-large;
    &-vertical {
      flex-direction: column;
      flex-flow: column-reverse;
    }
    .btn-cancel {
      position: relative;
      flex: 1;
      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        width: 1rpx;
        height: 100%;
        background: #f2f2f2;
      }
    }

    .btn-confirm {
      flex: 1;
    }
  }
}
</style>
