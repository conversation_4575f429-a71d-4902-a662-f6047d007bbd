/*
 * @Author: ho<PERSON><PERSON><PERSON>
 * @Date: 2025-03-04 11:51:12
 * @Description:
 * @LastEditTime: 2025-03-04 17:32:34
 * @LastEditors: houbaoguo
 */
import type { ExtractPropTypes } from 'vue'
import { commonProps, isNumber, isString, makeNumericProp, makeStringProp, truthProp } from '@/utils'

export const codeinputProps = {
  ...commonProps,
  /**
   * @description 键盘弹起时，是否自动上推页面
   */
  adjustPosition: truthProp,
  /**
   * @description 最大输入长度
   */
  maxlength: makeNumericProp(6),
  /**
   * @description 是否用圆点填充
   */
  dot: Boolean,
  /**
   * @description 显示模式，可选值：`box`-盒子模式，`line`-底部横线模式
   */
  mode: makeStringProp<'box' | 'line'>('box'),
  /**
   * @description 是否细边框
   */
  hairline: Boolean,
  /**
   * @description 字符间的距离
   */
  space: makeNumericProp(20),
  /**
   * @description 预置值
   */
  modelValue: makeNumericProp(''),
  /**
   * @description 是否自动获取焦点
   */
  focus: Boolean,
  /**
   * @description 字体是否加粗
   */
  bold: Boolean,
  /**
   * @description 字体颜色
   */
  fontColor: makeStringProp('#606266'),
  /**
   * @description 字体大小
   */
  fontSize: makeNumericProp(36),
  /**
   * @description 输入框的大小，宽
   */
  width: makeNumericProp(70),
  /**
   * @description 输入框的大小，高
   */
  height: makeNumericProp(70),
  /**
   * @description 是否隐藏原生键盘，如果想用自定义键盘的话，需设置此参数为true
   */
  disabledKeyboard: Boolean,
  /**
   * @description 边框和线条颜色
   */
  borderColor: makeStringProp('#c9cacc'),
  /**
   * @description 获取焦点时边框颜色
   */
  focusBorderColor: makeStringProp('#c9cacc'),
  /**
   * @description 光标颜色
   */
  cursorColor: makeStringProp('#606266'),
  /**
   * @description 是否禁止输入"."符号
   */
  disabledDot: truthProp,
}

export type CodeInputProps = ExtractPropTypes<typeof codeinputProps>

export const codeinputEmits = {
  'change': (val: string) => isString(val),
  'input': (val: string) => isString(val),
  'finish': (val: string) => isString(val),
  'update:modelValue': (val: number | string) => isString(val) || isNumber(val),

}

export type CodeInputEmits = typeof codeinputEmits
