<script setup lang="ts">
import type { CSSProperties } from 'vue'
import { getMainClass } from '@/utils'
import { codeinputEmits, codeinputProps } from './codeinput'

const props = defineProps(codeinputProps)

const emit = defineEmits(codeinputEmits)
const classes = computed(() => {
  return getMainClass(props, 'fx-code-input')
})
const inputValue = ref('')
const isFocus = ref(props.focus)

const codeLength = computed(() => {
  return Array.from({ length: Number(props.maxlength) })
})
const itemStyle = computed(() => {
  return (index: number) => {
    const style: CSSProperties = {
      width: `${props.width}rpx`,
      height: `${props.height}rpx`,
    }
    // 盒子模式下，需要额外进行处理
    if (props.mode === 'box') {
      // 设置盒子的边框，如果是细边框，则设置为0.5px宽度
      style.border = `${props.hairline ? 0.5 : 1}px solid ${props.borderColor}`
      // 如果盒子间距为0的话
      if (props.space === 0) {
        // 给第一和最后一个盒子设置圆角
        if (index === 0) {
          style.borderTopLeftRadius = '3px'
          style.borderBottomLeftRadius = '3px'
        }
        if (index === codeLength.value.length - 1) {
          style.borderTopRightRadius = '3px'
          style.borderBottomRightRadius = '3px'
        }
        // 最后一个盒子的右边框需要保留
        if (index !== codeLength.value.length - 1) {
          style.borderRight = 'none'
        }
      }
      // 设置盒子获取焦点时边框颜色
      if (isFocus.value && codeArray.value.length === index) {
        style.borderColor = props.focusBorderColor
      }
    }
    if (index !== codeLength.value.length - 1) {
      // 设置验证码字符之间的距离，通过margin-right设置，最后一个字符，无需右边框
      style.marginRight = `${props.space}rpx`
    }
    else {
      // 最后一个盒子的有边框需要保留
      style.marginRight = 0
    }

    return style
  }
})
const codeArray = computed(() => {
  return String(inputValue.value).split('')
})
const lineStyle = computed(() => {
  const style: CSSProperties = {}
  style.height = props.hairline ? '2px' : '4px'
  style.width = `${props.width}rpx`
  // 线条模式下，背景色即为边框颜色
  style.backgroundColor = props.borderColor
  return style
})

watch(() => props.modelValue, (val) => {
  // 转为字符串，超出部分截掉
  inputValue.value = String(val).substring(0, +props.maxlength)
}, {
  immediate: true,
})

function inputHandler(e: { detail: { value: string } }) {
  const value = e.detail.value
  emit('update:modelValue', value)

  inputValue.value = value
  // 是否允许输入“.”符号
  if (props.disabledDot) {
    nextTick(() => {
      inputValue.value = value.replace('.', '')
    })
  }
  // 未达到maxlength之前，发送change事件，达到后发送finish事件
  emit('change', value)
  // 修改通过v-model双向绑定的值
  emit('input', value)
  // 达到用户指定输入长度时，发出完成事件
  if (String(value).length >= Number(props.maxlength))
    emit('finish', value)
}
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <view :class="classes" :style="customStyle">
    <view
      v-for="(_item, index) in codeLength"
      :key="index"
      class="fx-code-input__item"
      :style="[itemStyle(index)]"
    >
      <view v-if="dot && codeArray.length > index" class="fx-code-input__item__dot" />
      <text
        v-else
        :style="{
          fontSize: `${props.fontSize}rpx`,
          fontWeight: bold ? 'bold' : 'normal',
          color: fontColor,
        }"
      >
        {{ codeArray[index] }}
      </text>
      <view
        v-if="mode === 'line'"
        class="fx-code-input__item__line"
        :style="[lineStyle]"
      />
      <!-- #ifndef APP-PLUS -->
      <view
        v-if="isFocus && codeArray.length === index"
        :style="{ backgroundColor: cursorColor }"
        class="fx-code-input__item__cursor"
      />
      <!-- #endif -->
    </view>
    <input
      :disabled="disabledKeyboard"
      type="number"
      :focus="focus"
      :value="inputValue"
      :maxlength="+maxlength"
      :adjustPosition="adjustPosition"
      class="fx-code-input__input"
      :style="{
        height: `${props.height}rpx`,
      }"
      @input="inputHandler"
      @focus="isFocus = true"
      @blur="isFocus = false"
    >
  </view>
</template>

<style lang="scss" scoped>
.fx-code-input {
  position: relative;
  display: flex;
  overflow: hidden;

  &__item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &__text {
      font-size: 15px;
      color: $code-input-content-color;
    }

    &__dot {
      width: 7px;
      height: 7px;
      background-color: #666;
      border-radius: 100px;
    }

    &__line {
      position: absolute;
      bottom: 0;
      width: 40px;
      height: 4px;
      background-color: $code-input-content-color;
      border-radius: 100px;
    }

    /* #ifndef APP-PLUS */
    &__cursor {
      position: absolute;
      top: 50%;
      left: 50%;
      width: $code-input-cursor-width;
      height: $code-input-cursor-height;
      transform: translate(-50%, -50%);
      animation: $code-input-cursor-animation-duration fx-cursor-flicker infinite;
    }

    /* #endif */

  }

  &__input {
    // 之所以需要input输入框，是因为有它才能唤起键盘
    // 这里将它设置为两倍的屏幕宽度，再将左边的一半移出屏幕，为了不让用户看到输入的内容
    position: absolute;
    top: 0;
    left: -325px;
    width: 700px;
    text-align: left;
    background-color: transparent;
  }
}

/* #ifndef APP-PLUS */
@keyframes fx-cursor-flicker {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

/* #endif */
</style>
