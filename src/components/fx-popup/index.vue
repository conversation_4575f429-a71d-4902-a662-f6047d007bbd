<!--
 * @Author: houbaoguo
 * @Date: 2025-02-14 09:00:25
 * @Description:
 * @LastEditTime: 2025-07-29 12:49:05
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import type { FxAnimationName } from '../fx-transition/types'
import { commonProps, getMainClass } from '@/utils'

type Position = 'center' | 'top' | 'bottom' | 'left' | 'right'
const props = withDefaults(defineProps<{
  visible: boolean
  overlay?: boolean
  position: Position
  transition?: FxAnimationName
  safeAreaInsetBottom?: boolean
  safeAreaInsetTop?: boolean
  transitionName?: string
  zIndex?: number
  maskCloseable?: boolean
  opacity?: number
}>(), {
  ...commonProps,
  visible: false,
  overlay: true,
  position: 'center',
  safeAreaInsetBottom: true,
  safeAreaInsetTop: true,
  zIndex: 2000,
  maskCloseable: true,
  opacity: 0.5,
})

const emit = defineEmits(['update:visible', 'clickPopup'])

const animationName: Record<Position, FxAnimationName> = {
  center: 'fade',
  top: 'slide-down',
  bottom: 'slide-up',
  left: 'slide-left',
  right: 'slide-right',
}

const innerVisible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emit('update:visible', val)
  },
})

// 使用滚动锁定功能
useScrollLockWithVisible(innerVisible)
const transitionName = computed<FxAnimationName>(() => {
  return props.transition ? props.transition : `${animationName[props.position]}`
})
const classes = computed(() => {
  return getMainClass(props, 'fx-popup', {
    [`fx-popup--${props.position}`]: true,
    [`fx-popup--safe-area-inset-top`]: props.position === 'top' && props.safeAreaInsetTop,
    [`fx-popup--safe-area-inset-bottom`]: props.position === 'bottom' && props.safeAreaInsetBottom,
  })
})
const windowHeight = uni.getWindowInfo().windowHeight
const centerY = computed(() => `${windowHeight / 2}px`)

function handleClickPopup() {
  emit('clickPopup')
}
</script>

<script lang="ts">
export default defineComponent({
  options: {
    styleIsolation: 'shared',
  },
})
</script>

<template>
  <fx-overlay
    v-if="overlay"
    v-model:visible="innerVisible"
    :close-on-click-overlay="maskCloseable"
    :z-index="zIndex"
    :opacity="opacity"
  />
  <fx-transition
    :custom-class="classes"
    :show="innerVisible"
    :name="transitionName"
    :custom-style="{ zIndex }"
    @tap.stop="handleClickPopup"
    @touchmove.stop="() => {}"
  >
    <slot />
  </fx-transition>
</template>

<style lang="scss">
.fx-popup {
  position: fixed;
  max-height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  &--top {
    top: 0;
  }
  &--bottom {
    left: 0;
    bottom: 0;
  }
  &--center {
    top: 50%;
    top: v-bind(centerY);
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    transform-origin: 50% 50%;
  }
  &--left {
    left: 0;
  }
  &--right {
    right: 0;
  }
  &--safe-area-inset-top {
    padding-top: var(--status-bar-height);
    padding-top: constant(safe-area-inset-top);
    padding-top: env(safe-area-inset-top);
  }
  &--safe-area-inset-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
  }
}
</style>
