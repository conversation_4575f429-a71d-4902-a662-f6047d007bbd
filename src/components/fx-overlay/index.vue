<!--
 * @Author: houbaoguo
 * @Date: 2025-02-13 17:58:56
 * @Description:
 * @LastEditTime: 2025-04-30 16:35:45
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
import { getMainClass, getMainStyle } from '@/utils'
import { overlayEmits, overlayProps } from './overlay'

const props = defineProps(overlayProps)

const emit = defineEmits(overlayEmits)

const classes = computed(() => {
  return getMainClass(props, 'fx-overlay', {
    [props.overlayClass]: true,
  })
})

const innerDuration = computed(() => {
  if (typeof props.duration === 'number')
    return props.duration

  return Number(props.duration)
})

const styles = computed(() => {
  return getMainStyle(props, {
    transitionDuration: `${innerDuration.value}ms`,
    zIndex: props.zIndex,
    ...props.overlayStyle,
    background: `rgba(0, 0, 0, ${props.opacity})`,
  })
})

function onClick(event: any) {
  emit('click', event)

  if (props.closeOnClickOverlay)
    emit('update:visible', false)
}

// 阻止触摸移动事件，防止背景页面滚动
function onTouchMove(event: any) {
  // 阻止事件冒泡和默认行为
  event.stopPropagation()
  event.preventDefault()
}
</script>

<template>
  <fx-transition
    :custom-class="classes"
    :custom-style="styles"
    :show="props.visible"
    name="fade"
    :duration="innerDuration"
    :destroy-on-close="props.destroyOnClose"
    @click="onClick"
    @touchmove.stop.prevent="onTouchMove"
  >
    <slot />
  </fx-transition>
</template>

<style lang="scss" scoped></style>
