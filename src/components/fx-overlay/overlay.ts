/*
 * @Author: houbaog<PERSON>
 * @Date: 2025-02-13 17:58:56
 * @Description:
 * @LastEditTime: 2025-04-30 16:34:35
 * @LastEditors: houbaoguo
 */
import type { CSSProperties, ExtractPropTypes, PropType } from 'vue'
import { commonProps, isBoolean, makeNumericProp, makeStringProp, truthProp } from '@/utils'

export const overlayProps = {
  ...commonProps,
  /**
   * @description 控制遮罩的显示/隐藏
   */
  visible: Boolean,
  /**
   * @description 自定义遮罩层级
   */
  zIndex: makeNumericProp(1999),
  /**
   * @description 显示/隐藏的动画时长，单位毫秒
   */
  duration: makeNumericProp(300),
  /**
   * @description 自定义遮罩类名
   */
  overlayClass: makeStringProp(''),
  /**
   * @description 自定义遮罩样式
   */
  overlayStyle: Object as PropType<CSSProperties>,
  /**
   * @description 遮罩显示时的背景是否锁定
   */
  lockScroll: Boolean,
  /**
   * @description 点击遮罩时是否关闭
   */
  closeOnClickOverlay: truthProp,
  /**
   * @description 是否保留遮罩关闭后的内容
   */
  destroyOnClose: Boolean,
  /**
   * @description 遮罩的透明度
   */
  opacity: makeNumericProp(0.5),
}

export type OverlayProps = ExtractPropTypes<typeof overlayProps>

export const overlayEmits = {
  'update:visible': (visible: boolean) => isBoolean(visible),
  'click': (evt: any) => evt instanceof Object,
}

export type OverlayEmits = typeof overlayEmits
