<script setup lang="ts">
import type { ButtonOpenType } from '@uni-helper/uni-app-types'

interface Props {
  plain?: boolean
  block?: boolean
  round?: boolean
  square?: boolean
  disabled?: boolean
  customStyle?: string | Record<string, any> | Array<string | Record<string, any>>
  openType?: ButtonOpenType | 'getRealtimePhoneNumber'
  radius?: number | string
  hoverClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  plain: false,
  block: false,
  round: false,
  square: false,
  disabled: false,
  radius: 8,
  hoverClass: 'fx-button-hover',
})

const emit = defineEmits([
  'click',
  'getphonenumber',
  'getrealtimephonenumber',
  'getuserinfo',
  'opensetting',
  'launchapp',
  'contact',
  'chooseavatar',
  'agreeprivacyauthorization',
  'addgroupapp',
  'chooseaddress',
  'chooseinvoicetitle',
  'subscribe',
  'login',
  'im',
  'error',
])

const computedStyle = computed(() => {
// 基础样式对象
  const baseStyleObj = {
    borderRadius: `${Number.parseFloat(props.radius as string)}rpx`,
  }
  if (!props.customStyle) return baseStyleObj
  // 驼峰转换
  const camelToKebab = (str: string) =>
    str.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase()
  // 字符串
  if (typeof props.customStyle === 'string') {
    // 处理字符串，转换为kebab-case并合并
    const customStyles = props.customStyle.split(';')
      .filter(Boolean)
      .map((rule) => {
        const [key, value] = rule.split(':').map(s => s.trim())
        return `${camelToKebab(key)}:${value}`
      })
      .join(';')
    return `border-radius: ${baseStyleObj.borderRadius};${customStyles}`
  }
  // 对象/数组
  const normalizeStyles = () => {
    if (Array.isArray(props.customStyle)) {
      return Object.assign({}, ...props.customStyle)
    }
    return props.customStyle
  }
  return {
    ...baseStyleObj,
    ...normalizeStyles(),
  }
})

function handleClick(event: MouseEvent) {
  if (!props.disabled) {
    emit('click', event)
  }
}

function getPhoneNumber(event: any) {
  emit('getphonenumber', event)
}

function getRealtimePhoneNumber(event: any) {
  emit('getrealtimephonenumber', event)
}

function getUserInfo(event: any) {
  emit('getuserinfo', event)
}

function openSetting(event: any) {
  emit('opensetting', event)
}

function launchApp(event: any) {
  emit('launchapp', event)
}

function contact(event: any) {
  emit('contact', event)
}

function chooseAvatar(event: any) {
  emit('chooseavatar', event)
}

function agreePrivacyAuthorization(event: any) {
  emit('agreeprivacyauthorization', event)
}

function addGroupApp(event: any) {
  emit('addgroupapp', event)
}

function chooseAddress(event: any) {
  emit('chooseaddress', event)
}

function chooseInvoiceTitle(event: any) {
  emit('chooseinvoicetitle', event)
}

function subscribe(event: any) {
  emit('subscribe', event)
}

function login(event: any) {
  emit('login', event)
}

function im(event: any) {
  emit('im', event)
}

function error(event: any) {
  emit('error', event)
}
</script>

<template>
  <!-- 内置组件不支持v-bind，需要某个原生属性需要手动传递一下 -->
  <button
    class="fx-button"
    :class="[
      {
        'is-plain': plain,
        'is-block': block,
        'is-round': round,
        'is-square': square,
        'is-disabled': disabled,
      },
    ]"
    :disabled="disabled"
    :open-type="openType as unknown as ButtonOpenType"
    :style="computedStyle"
    :hover-class="hoverClass"
    @click="handleClick"
    @getphonenumber="getPhoneNumber"
    @getrealtimephonenumber="getRealtimePhoneNumber"
    @getuserinfo="getUserInfo"
    @opensetting="openSetting"
    @launchapp="launchApp"
    @contact="contact"
    @chooseavatar="chooseAvatar"
    @agreeprivacyauthorization="agreePrivacyAuthorization"
    @addgroupapp="addGroupApp"
    @chooseaddress="chooseAddress"
    @chooseinvoicetitle="chooseInvoiceTitle"
    @subscribe="subscribe"
    @login="login"
    @im="im"
    @error="error"
  >
    <slot />
  </button>
</template>

<style scoped lang="scss">
.fx-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 12rpx 24rpx;
  font-size: $font-size-base;
  text-align: center;
  vertical-align: middle;
  text-size-adjust: 100%;
  transition: all 0.2s ease;
  cursor: pointer;
  background-color: var(--fx-primary-color);
  border: 2rpx solid transparent;
  color: #fff;
  appearance: none;
  &::after {
    border: none;
  }

  &-hover {
    background-color: var(--fx-primary-color);
    filter: brightness(0.9);
  }

  // 块级按钮
  &.is-block {
    width: 100%;
    display: flex;
  }

  // 朴素按钮
  &.is-plain {
    background-color: transparent;
    border-color: var(--fx-primary-color);
    color: var(--fx-primary-color);

    &.fx-button-hover {
      background-color: transparent;
      // background-color: rgba($primary-color, 0.2);
    }
  }

  // 圆形按钮
  &.is-round {
    border-radius: 999rpx !important;
  }

  // 方形按钮
  &.is-square {
    border-radius: 0 !important;
  }

  // 禁用状态
  &.is-disabled {
    color: #fff;
    background-color: var(--fx-primary-color);
    opacity: 0.5;
    cursor: not-allowed;

    &.fx-button-hover {
      opacity: 0.5;
      background-color: var(--fx-primary-color);
    }

    &.is-plain {
      &.fx-button-hover {
        background-color: transparent;
      }
    }
  }
}
</style>
