<script setup lang="ts">
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '',
  },
  titleColor: {
    type: String,
    default: '',
  },
  // 是否展示左侧箭头
  leftShow: {
    type: Boolean,
    default: true,
  },
  leftColor: {
    type: String,
    default: '',
  },
  // 是否固定到顶部
  fixed: {
    type: Boolean,
    default: true,
  },
  // 固定在顶部时，是否在标签位置生成一个等高的占位元素
  placeholder: {
    type: Boolean,
    default: true,
  },
  // 是否开启顶部安全区适配
  safeAreaInsetTop: {
    type: Boolean,
    default: true,
  },
  // 导航栏 z-index
  zIndex: {
    type: [Number, String],
    default: 100,
  },
  background: {
    type: String,
    default: '#ffffff',
  },
})

const emit = defineEmits<{
  (e: 'clickLeft'): boolean
}>()

// 导航栏高度
const navBarHeight = ref(44)
// 状态栏高度
const statusBarHeight = ref(0)

onMounted(() => {
  // 获取系统信息
  const sysInfo = uni.getSystemInfoSync()
  // 设置状态栏高度
  statusBarHeight.value = sysInfo.statusBarHeight || 0
  console.log(statusBarHeight.value + navBarHeight.value)
})

// 导航栏样式
const navBarStyle = computed(() => {
  return {
    'height': `${navBarHeight.value}px`,
    'z-index': props.zIndex,
    'padding-top': props.safeAreaInsetTop ? `${statusBarHeight.value}px` : '0',
    'background': props.background,
  }
})

const placeholderStyle = computed(() => {
  return {
    height: `${navBarHeight.value + statusBarHeight.value}px`,
  }
})

const leftStyle = computed(() => {
  let leftColor = props.leftColor
  if (props.background === 'transparent' && !leftColor) {
    leftColor = '#fff'
  }
  if (props.background !== 'transparent' && !leftColor) {
    leftColor = '#333'
  }
  return {
    color: leftColor,
  }
})

const titleStyle = computed(() => {
  let titleColor = props.titleColor
  if (props.background === 'transparent' && !titleColor) {
    titleColor = '#fff'
  }
  if (props.background !== 'transparent' && !titleColor) {
    titleColor = '#333'
  }
  return {
    color: titleColor,
  }
})

// 返回上一页
function handleBack() {
  if (props.leftShow) {
    const isNext = emit('clickLeft')
    if (!isNext) {
      uni.navigateBack()
    }
  }
}
</script>

<template>
  <view
    class="fx-nav-bar"
    :class="{ 'fx-nav-bar--fixed': fixed, 'safe-area-inset-top': safeAreaInsetTop }"
    :style="navBarStyle"
  >
    <slot name="nav-content">
      <view class="fx-nav-bar__content">
        <view class="fx-nav-bar__left" @click="handleBack">
          <slot v-if="leftShow" name="left">
            <view
              i-carbon:chevron-left
              h-48rpx
              w-48rpx
              :style="leftStyle"
            />
          </slot>
        </view>

        <view class="fx-nav-bar__center">
          <slot name="content">
            <text
              class="fx-nav-bar__title"
              :style="titleStyle"
            >
              {{ title }}
            </text>
          </slot>
        </view>

        <view class="fx-nav-bar__right">
          <slot name="right" />
        </view>
      </view>
    </slot>
  </view>

  <!-- 占位元素 -->
  <view
    v-if="fixed && placeholder"
    class="fx-nav-bar__placeholder"
    :style="placeholderStyle"
  />
</template>

<style lang="scss">
.fx-nav-bar {
  position: relative;

  &--fixed {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
  }

  &__content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 12px;
  }

  &__left,
  &__right {
    min-width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__center {
    flex: 1;
    text-align: center;
    padding: 0 10px;
    overflow: hidden;
  }

  &__title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__placeholder {
    width: 100%;
  }
}

.safe-area-inset-top {
  padding-top: var(--status-bar-height);
}
</style>
