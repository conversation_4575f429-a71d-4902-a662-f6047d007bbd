export interface DraggablePosition {
  x: number
  y: number
}

export interface DraggableBoundary {
  top?: number
  left?: number
  right?: number
  bottom?: number
}

export type DraggableEdge = 'left' | 'right' | 'top' | 'bottom'

export type DraggableAdsorbDirection = 'horizontal' | 'vertical' | 'all' | DraggableEdge[]

export interface DraggableProps {
  /** 是否启用拖拽 */
  disabled?: boolean
  /** 拖拽边界，默认为整个屏幕 */
  boundary?: DraggableBoundary
  /** 是否启用贴边吸附 */
  adsorb?: boolean
  /** 贴边吸附的距离阈值 */
  adsorbDistance?: number
  /** 吸附方向控制 */
  adsorbDirection?: DraggableAdsorbDirection
  /** 初始位置 */
  initialPosition?: DraggablePosition
  /** 自定义样式 */
  customStyle?: string | Record<string, any>
  /** z-index */
  zIndex?: number
}

export interface DraggableEmits {
  (e: 'dragStart', position: DraggablePosition): void
  (e: 'dragMove', position: DraggablePosition): void
  (e: 'dragEnd', position: DraggablePosition): void
  (e: 'adsorbed', edge: DraggableEdge): void
}
