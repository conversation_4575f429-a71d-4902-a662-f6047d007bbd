<script setup lang="ts">
import type { DraggableEmits, DraggableProps } from './types'
import { useDraggable } from '@/composables/useDraggable'

const props = withDefaults(defineProps<DraggableProps>(), {
  disabled: false,
  adsorb: true,
  adsorbDistance: 20,
  adsorbDirection: 'all',
  zIndex: 999,
})

const emit = defineEmits<DraggableEmits>()

// 生成唯一ID
const uniqueId = `fx-draggable-${Math.random().toString(36).substring(2, 11)}`

// 获取当前组件实例
const instance = getCurrentInstance()

// 使用拖拽组合函数
const {
  isDragging,
  isInitialized,
  position,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd,
  initializePosition,
  getScreenSize,
} = useDraggable({
  elementId: uniqueId,
  instance,
  disabled: toRef(props, 'disabled'),
  boundary: computed(() => props.boundary || {}),
  adsorb: toRef(props, 'adsorb'),
  adsorbDistance: toRef(props, 'adsorbDistance'),
  adsorbDirection: toRef(props, 'adsorbDirection'),
  initialPosition: computed(() => props.initialPosition || { x: 0, y: 0 }),
  onDragStart: pos => emit('dragStart', pos),
  onDragMove: pos => emit('dragMove', pos),
  onDragEnd: pos => emit('dragEnd', pos),
  onAdsorbed: edge => emit('adsorbed', edge),
})

// 计算样式
const computedStyle = computed(() => {
  const baseStyle = {
    position: 'fixed' as const,
    left: `${position.value.x}px`,
    top: `${position.value.y}px`,
    zIndex: props.zIndex,
    transition: (isDragging.value || !isInitialized.value) ? 'none' : 'all 0.3s ease',
    cursor: props.disabled ? 'default' : 'move',
  }
  if (!props.customStyle) return baseStyle

  if (typeof props.customStyle === 'string') {
    return { ...baseStyle, ...parseStyleString(props.customStyle) }
  }

  return { ...baseStyle, ...props.customStyle }
})

// 解析样式字符串
function parseStyleString(styleStr: string) {
  const styles: Record<string, string> = {}
  styleStr.split(';').forEach((rule) => {
    const [key, value] = rule.split(':').map(s => s.trim())
    if (key && value) {
      const camelKey = key.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
      styles[camelKey] = value
    }
  })
  return styles
}

// 生命周期
onMounted(async () => {
  getScreenSize()
  await nextTick()
  await initializePosition()
})

// 监听屏幕尺寸变化
onMounted(() => {
  const handleResize = () => {
    getScreenSize()
  }

  // 监听页面尺寸变化
  uni.onWindowResize?.(handleResize)

  onUnmounted(() => {
    uni.offWindowResize?.(handleResize)
  })
})
</script>

<template>
  <view
    :id="uniqueId"
    class="fx-draggable"
    :class="{ 'is-dragging': isDragging, 'is-disabled': disabled }"
    :style="computedStyle"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchEnd"
  >
    <slot />
  </view>
</template>

<style scoped lang="scss">
.fx-draggable {
  display: inline-block;
  user-select: none;
  touch-action: none;

  /* &.is-dragging {
    opacity: 0.8;
  } */

  &.is-disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }
}
</style>
