<!--
 * @Author: houbaoguo
 * @Date: 2025-02-17 11:06:14
 * @Description:
 * @LastEditTime: 2025-04-11 15:12:24
 * @LastEditors: houbaoguo
-->
<script setup lang="ts">
const props = withDefaults(defineProps<{
  type?: 'text' | 'number' | 'idcard' | 'digit' | 'tel' | 'safe-password' | 'nickname'
  placeholder?: string
  modelValue?: string
  height?: number | string
  placeholderStyle?: string
  placeholderClass?: string
  disabled?: boolean
  maxlength?: number
  cursorSpacing?: number
  focus?: boolean
  confirmType?: 'done' | 'go' | 'next' | 'search' | 'send'
  confirmHold?: boolean
  cursor?: number
  selectionStart?: number
  selectionEnd?: number
  adjustPosition?: boolean
  holdKeyboard?: boolean
  customStyle?: string
  clearable?: boolean
}>(), {
  type: 'text',
  placeholder: '请输入',
  modelValue: '',
  height: 44,
  placeholderStyle: 'color: #ACB4BE;',
  placeholderClass: '',
  disabled: false,
  maxlength: 140,
  cursorSpacing: 0,
  focus: false,
  confirmType: 'done',
  confirmHold: false,
  cursor: 0,
  selectionStart: 0,
  selectionEnd: 0,
  adjustPosition: true,
  holdKeyboard: false,
  customStyle: '',
  clearable: false,
})
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'focus', value: string): void
  (e: 'blur', value: string): void
  (e: 'confirm', value: string): void
  (e: 'update:holdKeyboard', value: boolean): void
}>()

const isFocus = ref(false)

const heightStyle = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}rpx`
  }
  return props.height
})

function handleInput(e: any) {
  emit('update:modelValue', e.target.value)
}
function handleFocus(e: any) {
  isFocus.value = true
  emit('focus', e.target.value)
}
function handleBlur(e: any) {
  isFocus.value = false
  emit('blur', e.target.value)
}
function handleConfirm(e: any) {
  emit('confirm', e.target.value)
}
function handleClear() {
  emit('update:modelValue', '')
  emit('update:holdKeyboard', true)
}
</script>

<template>
  <view
    class="fx-input"
    :class="{ clearable: clearable && modelValue && isFocus }"
  >
    <!-- 内置组件不支持v-bind，需要某个原生属性需要手动传递一下 -->
    <input
      :style="customStyle"
      class="fx-input__input"
      :placeholder="placeholder"
      :value="modelValue"
      :type="type"
      :placeholder-style="placeholderStyle"
      :placeholder-class="placeholderClass"
      :disabled="disabled"
      :maxlength="maxlength"
      :cursor-spacing="cursorSpacing"
      :focus="focus"
      :confirm-type="confirmType"
      :confirm-hold="confirmHold"
      :cursor="cursor"
      :selection-start="selectionStart"
      :selection-end="selectionEnd"
      :adjust-position="adjustPosition"
      :hold-keyboard="holdKeyboard"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    >
    <view
      v-if="clearable && modelValue && isFocus"
      class="fx-input__clear-wrap"
      @click="handleClear"
    >
      <image
        src="@/static/images/common/input-clear.png"
        class="fx-input__clear"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.fx-input {
  position: relative;
  height: v-bind(heightStyle);
  overflow: hidden;
  &.clearable {
    padding-right: 62rpx;
  }
  .fx-input__input {
    height: v-bind(heightStyle);
    background-color: transparent;
    padding-left: 16rpx;
  }
  .fx-input__clear-wrap {
    position: absolute;
    right: 24rpx;
    top: 50%;
    transform: translateY(-50%);
    width: 44rpx;
    height: 44rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    .fx-input__clear {
      width: 36rpx;
      height: 36rpx;
    }
  }
}
</style>
