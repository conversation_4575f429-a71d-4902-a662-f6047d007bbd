/**
 * 确认收货事件管理
 * 基于通用事件总线实现的业务特定事件管理
 */

import { BaseEventManager, useEvent, useEventLifecycle } from '@/utils/eventBus'

// 事件名称常量
export const CONFIRM_RECEIPT_EVENT_NAME = 'confirmReceiptCallback'

/**
 * 确认收货事件管理器
 */
class ConfirmReceiptEventManager extends BaseEventManager<ConfirmReceiptCallbackData> {
  private static instance: ConfirmReceiptEventManager

  private constructor() {
    super(CONFIRM_RECEIPT_EVENT_NAME)
  }

  static getInstance(): ConfirmReceiptEventManager {
    if (!ConfirmReceiptEventManager.instance) {
      ConfirmReceiptEventManager.instance = new ConfirmReceiptEventManager()
    }
    return ConfirmReceiptEventManager.instance
  }

  /**
   * 发布确认收货回调事件
   * @param data 回调数据
   */
  publish(data: ConfirmReceiptCallbackData): void {
    console.log('发布确认收货回调事件:', data)
    super.publish(data)
  }

  /**
   * 订阅确认收货回调事件
   * @param callback 回调函数
   */
  subscribe(callback: (data: ConfirmReceiptCallbackData) => void): void {
    console.log('订阅确认收货回调事件')
    super.subscribe(callback)
  }

  /**
   * 取消订阅确认收货回调事件
   * @param callback 回调函数
   */
  unsubscribe(callback?: (data: ConfirmReceiptCallbackData) => void): void {
    console.log('取消订阅确认收货回调事件')
    super.unsubscribe(callback)
  }
}

/**
 * 确认收货事件管理器实例
 */
export const confirmReceiptEventManager = ConfirmReceiptEventManager.getInstance()

/**
 * 便捷的确认收货事件工具函数
 */
export const confirmReceiptEvent = {
  /**
   * 订阅确认收货回调
   * @param callback 回调函数
   */
  subscribe: (callback: (data: ConfirmReceiptCallbackData) => void) => {
    confirmReceiptEventManager.subscribe(callback)
  },

  /**
   * 取消订阅确认收货回调
   * @param callback 回调函数
   */
  unsubscribe: (callback?: (data: ConfirmReceiptCallbackData) => void) => {
    confirmReceiptEventManager.unsubscribe(callback)
  },

  /**
   * 发布确认收货回调
   * @param data 回调数据
   */
  publish: (data: ConfirmReceiptCallbackData) => {
    confirmReceiptEventManager.publish(data)
  },

  /**
   * 清除所有订阅
   */
  clear: () => {
    confirmReceiptEventManager.clear()
  },

  /**
   * 获取订阅数量
   */
  getSubscriberCount: () => {
    return confirmReceiptEventManager.getSubscriberCount()
  },
}

/**
 * Vue 组合函数：使用确认收货事件
 * @param callback 回调函数
 * @returns 取消订阅函数
 */
export function useConfirmReceiptEvent(
  callback: (data: ConfirmReceiptCallbackData) => void,
): () => void {
  return useEvent(confirmReceiptEventManager, callback)
}

/**
 * Vue 生命周期钩子：自动管理确认收货事件订阅
 * @param callback 回调函数
 */
export function useConfirmReceiptEventLifecycle(
  callback: (data: ConfirmReceiptCallbackData) => void,
): void {
  useEventLifecycle(confirmReceiptEventManager, callback)
}
