@use 'sass:color';

// 基础变量定义
// 主色
$primary-color: #387BFF;

// 字体
$font-size-base: 28rpx;
$font-size-small: 24rpx;
$font-size-large: 32rpx;

// CSS 变量
:root {
  --fx-primary-color: #{$primary-color};
  --fx-primary-hover-color: #{color.adjust($primary-color, $lightness: -10%)};
  --fx-primary-active-color: #{color.adjust($primary-color, $lightness: -15%)};
}

// nimation
$animation-duration: var(--fx-animation-duration, 0.25s) !default;
$animation-timing-fun: var(--fx-animation-timing-fun, cubic-bezier(0.55, 0.085, 0.68, 0.53)) !default;

// Overlay
$overlay-bg-color: var(--fx-overlay-bg-color, rgba(0, 0, 0, 0.5));

// CodeInput
$code-input-cursor-width: var(--fx-code-input-cursor-width, 1px) !default;
$code-input-cursor-height: var(--fx-code-input-cursor-height, 40%) !default;
$code-input-cursor-animation-duration: 1s;
$code-input-cursor-animation-name: nut-cursor-flicker;
$code-input-content-color: var(--fx-code-input-content-color, #606266) !default;

//  swiper
$swiper-pagination-item-width: var(--fx-swiper-pagination-item-width, 8px) !default;
$swiper-pagination-item-height: var(--fx-swiper-pagination-item-height, 3px) !default;
$swiper-pagination-item-margin-right: var(--fx-swiper-pagination-item-margin-right, 7px) !default;
$swiper-pagination-item-border-radius: var(--fx-swiper-pagination-item-border-radius, 2px) !default;

// inputnumber

$inputnumber-icon-color: var(--fx-inputnumber-icon-color, #1a1a1a) !default;
$inputnumber-icon-void-color: var(--fx-inputnumber-icon-void-color, #ccc) !default;
$inputnumber-icon-size: var(--fx-inputnumber-icon-size, 20px) !default;
$inputnumber-input-font-size: var(--fx-inputnumber-input-font-size, 12px) !default;
$inputnumber-input-font-color: var(--fx-inputnumber-input-font-color, #1a1a1a) !default;
$inputnumber-input-background-color: var(--fx-inputnumber-input-background-color, #f5f5f5) !default;
$inputnumber-input-border-radius: var(--fx-inputnumber-input-border-radius, 4px) !default;
$inputnumber-input-width: var(--fx-inputnumber-input-width, 40px) !default;
$inputnumber-input-margin: var(--fx-inputnumber-input-margin, 0 6px) !default;
$inputnumber-input-border: var(--fx-inputnumber-input-border, 0) !default;
$inputnumber-border: var(--fx-inputnumber-border, 0) !default;
$inputnumber-border-radius: var(--fx-inputnumber-border-radius, 0) !default;
$inputnumber-height: var(--fx-inputnumber-height, auto) !default;
$inputnumber-line-height: var(--fx-inputnumber-line-height, normal) !default;
$inputnumber-border-box: var(--fx-inputnumber-border-box, content-box) !default;
$inputnumber-display: var(--fx-inputnumber-display, inline-flex) !default;