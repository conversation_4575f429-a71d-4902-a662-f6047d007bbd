@use '../mixins/make-animation' as *;

   @keyframes rotation {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }
   
   
   // 跳动
    @keyframes fxJump {
        to {
            transform: scale3d(0.8, 1, 0.9) translateY(-10px);
        }
    }

    @keyframes fxJump {
        to {
            transform: scale3d(0.8, 1, 0.9) translateY(-10px);
        }
    }

    @keyframes fxJumpOne {
        50% {
            transform: scale3d(0.8, 1, 0.9) translateY(-10px);
        }

        100% {
            transform: scale3d(1, 1, 1) translateY(0);
        }
    }

    @keyframes fxJumpOne {
        50% {
            transform: scale3d(0.8, 1, 0.9) translateY(-10px);
        }

        100% {
            transform: scale3d(1, 1, 1) translateY(0);
        }
    }

    // 闪烁
    @keyframes fxBlink {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }

    @keyframes fxBlink {
        0% {
            opacity: 0;
        }

        100% {
            opacity: 1;
        }
    }

    // 呼吸
    @keyframes fxBreathe {
        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.2);
        }
    }

    @keyframes fxBreathe {
        0%,
        100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.2);
        }
    }

    // 闪现
    @keyframes fxFlash {
        0%,
        50%,
        100% {
            opacity: 1;
        }

        25%,
        75% {
            opacity: 0;
        }
    }

    @keyframes fxFlash {
        0%,
        50%,
        100% {
            opacity: 1;
        }

        25%,
        75% {
            opacity: 0;
        }
    }

    // 弹动
    @keyframes fxBounce {
        0%,
        20%,
        53%,
        100% {
            transform: translate3d(0, 0, 0);
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        }

        40%,
        43% {
            transform: translate3d(0, -30px, 0) scaleY(1.1);
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        }

        70% {
            transform: translate3d(0, -15px, 0) scaleY(1.05);
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        }

        80% {
            transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
            transform: translate3d(0, 0, 0) scaleY(0.95);
        }

        90% {
            transform: translate3d(0, -4px, 0) scaleY(1.02);
        }
    }

    @keyframes fxBounce {
        0%,
        20%,
        53%,
        100% {
            transform: translate3d(0, 0, 0);
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        }

        40%,
        43% {
            transform: translate3d(0, -30px, 0) scaleY(1.1);
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        }

        70% {
            transform: translate3d(0, -15px, 0) scaleY(1.05);
            animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        }

        80% {
            transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
            transform: translate3d(0, 0, 0) scaleY(0.95);
        }

        90% {
            transform: translate3d(0, -4px, 0) scaleY(1.02);
        }
    }

    // 抖动
    @keyframes fxShake {
        0% {
            transform: translateX(0);
        }

        6.5% {
            transform: translateX(-6px) rotateY(-9deg);
        }

        18.5% {
            transform: translateX(5px) rotateY(7deg);
        }

        31.5% {
            transform: translateX(-3px) rotateY(-5deg);
        }

        43.5% {
            transform: translateX(2px) rotateY(3deg);
        }

        50% {
            transform: translateX(0);
        }
    }

    @keyframes fxShake {
        0% {
            transform: translateX(0);
        }

        6.5% {
            transform: translateX(-6px) rotateY(-9deg);
        }

        18.5% {
            transform: translateX(5px) rotateY(7deg);
        }

        31.5% {
            transform: translateX(-3px) rotateY(-5deg);
        }

        43.5% {
            transform: translateX(2px) rotateY(3deg);
        }

        50% {
            transform: translateX(0);
        }
    }