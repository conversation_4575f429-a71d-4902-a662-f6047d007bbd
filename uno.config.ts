/*
 * @Author: houbaoguo
 * @Date: 2025-06-17 10:18:40
 * @Description:
 * @LastEditTime: 2025-07-09 10:55:16
 * @LastEditors: houbaoguo
 */
import type { Preset, SourceCodeTransformer } from 'unocss'
import {
  defineConfig,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

import presetWeapp from 'unocss-preset-weapp'
import {
  extractorAttributify,
  transformerClass,
} from 'unocss-preset-weapp/transformer'
import remToRpxPreset from './rem-to-rpx'

const { presetWeappAttributify, transformerAttributify }
  = extractorAttributify()

export default defineConfig({
  presets: [
    presetWeapp() as Preset,
    presetWeappAttributify() as Preset,
    remToRpxPreset({ baseFontSize: 4 }) as Preset,
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  /**
   * 自定义快捷语句
   * @see https://github.com/unocss/unocss#shortcuts
   */
  shortcuts: {},
  transformers: [
    // 启用 @apply 功能
    transformerDirectives({
      enforce: 'pre',
    }),
    // 启用 () 分组功能
    transformerVariantGroup(),
    transformerAttributify() as unknown as SourceCodeTransformer,
    transformerClass() as unknown as SourceCodeTransformer,
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
    [
      /^ellipsis-(\d+)-lines$/,
      ([, lines]) => ({
        'display': '-webkit-box',
        'overflow': 'hidden',
        'text-overflow': 'ellipsis',
        '-webkit-box-orient': 'vertical',
        '-webkit-line-clamp': Number(lines),
        'word-break': 'break-all',
      }),
    ],
    // 加载动画 - 支持通过字体大小控制图标大小
    [
      /^loader(?:-(.+))?$/,
      ([, color]) => {
        const borderColor = color ? `#${color}` : '#514b82'
        return {
          'width': '1em',
          'height': '1em',
          'aspect-ratio': '1',
          'border-radius': '50%',
          'border': `0.16em solid ${borderColor}`,
          'animation': 'loader-clip 0.8s infinite linear alternate, loader-rotate 1.6s infinite linear',
          'display': 'inline-block',
        }
      },
    ],
  ],
  preflights: [
    {
      getCSS: () => `
        @keyframes loader-clip {
          0% { clip-path: polygon(50% 50%,0 0, 50% 0%, 50% 0%, 50% 0%, 50% 0%, 50% 0%); }
          12.5% { clip-path: polygon(50% 50%,0 0, 50% 0%, 100% 0%, 100% 0%, 100% 0%, 100% 0%); }
          25% { clip-path: polygon(50% 50%,0 0, 50% 0%, 100% 0%, 100% 100%, 100% 100%, 100% 100%); }
          50% { clip-path: polygon(50% 50%,0 0, 50% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%); }
          62.5% { clip-path: polygon(50% 50%,100% 0, 100% 0%, 100% 0%, 100% 100%, 50% 100%, 0% 100%); }
          75% { clip-path: polygon(50% 50%,100% 100%, 100% 100%, 100% 100%, 100% 100%, 50% 100%, 0% 100%); }
          100% { clip-path: polygon(50% 50%,50% 100%, 50% 100%, 50% 100%, 50% 100%, 50% 100%, 0% 100%); }
        }
        @keyframes loader-rotate {
          0% { transform: scaleY(1) rotate(0deg); }
          49.99% { transform: scaleY(1) rotate(135deg); }
          50% { transform: scaleY(-1) rotate(0deg); }
          100% { transform: scaleY(-1) rotate(-135deg); }
        }
      `,
    },
  ],
})
