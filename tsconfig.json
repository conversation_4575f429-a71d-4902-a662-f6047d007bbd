{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "jsxImportSource": "vue", "lib": ["DOM", "DOM.Iterable", "ESNext"], "useDefineForClassFields": true, "baseUrl": ".", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "types": ["@dcloudio/types", "@dcloudio/uni-app", "@uni-helper/uni-app-types", "miniprogram-api-typings", "@types/node"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "outDir": "./dist", "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "verbatimModuleSyntax": true, "ignoreDeprecations": "5.0", "skipLibCheck": true}, "vueCompilerOptions": {"plugins": ["@uni-helper/uni-app-types/volar-plugin"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts"], "exclude": ["dist", "node_modules", "uni_modules", "src/utils/sm-crypto", "src/utils/sm-crypto/**/*"]}